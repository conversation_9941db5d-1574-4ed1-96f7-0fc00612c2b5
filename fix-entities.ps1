# PowerShell script to fix HTML entities in TypeScript and React files
Write-Host "🔍 Finding and fixing HTML entities in TypeScript/React files..." -ForegroundColor Cyan

$files = Get-ChildItem -Recurse -Path src -Include *.tsx,*.ts,*.jsx,*.js
$modifiedCount = 0

foreach ($file in $files) {
    try {
        $content = Get-Content $file.FullName -Raw
        $originalContent = $content
        
        # Replace HTML entities
        $content = $content -replace "&quot;", '"'
        $content = $content -replace "&apos;", "'"
        $content = $content -replace "&gt;", ">"
        $content = $content -replace "&lt;", "<"
        $content = $content -replace "&amp;", "&"
        
        if ($content -ne $originalContent) {
            Set-Content -Path $file.FullName -Value $content -NoNewline
            Write-Host "✅ Fixed: $($file.FullName)" -ForegroundColor Green
            $modifiedCount++
        }
    }
    catch {
        Write-Host "❌ Error processing $($file.FullName): $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n📊 Summary:" -ForegroundColor Yellow
Write-Host "- Total files checked: $($files.Count)" -ForegroundColor White
Write-Host "- Files modified: $modifiedCount" -ForegroundColor Green
Write-Host "- Files unchanged: $($files.Count - $modifiedCount)" -ForegroundColor White

if ($modifiedCount -gt 0) {
    Write-Host "`n🎉 HTML entities have been fixed! Run type-check again to verify." -ForegroundColor Green
} else {
    Write-Host "`n✨ No HTML entities found to fix." -ForegroundColor Green
} 