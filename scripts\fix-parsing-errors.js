#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Configuration
const rootDir = path.resolve(__dirname, '..');
const srcDir = path.join(rootDir, 'src');

// Statistics
let stats = {
  filesProcessed: 0,
  filesModified: 0,
  jsxElementsFixed: 0,
  commasFixed: 0,
  stringsFixed: 0,
  expressionsFixed: 0,
  errors: 0,
};

/**
 * Check if file should be processed
 */
function shouldProcessFile(filePath) {
  const ext = path.extname(filePath);
  return ['.ts', '.tsx', '.js', '.jsx'].includes(ext) && 
         !filePath.includes('node_modules') && 
         !filePath.includes('.next') &&
         !filePath.includes('dist') &&
         !filePath.includes('build');
}

/**
 * Find all files to process
 */
function findFiles(dir) {
  let results = [];
  
  try {
    const files = fs.readdirSync(dir);
    
    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        results = results.concat(findFiles(filePath));
      } else if (shouldProcessFile(filePath)) {
        results.push(filePath);
      }
    }
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error.message);
    stats.errors++;
  }
  
  return results;
}

/**
 * Fix JSX element issues - '{' or JSX element expected
 */
function fixJSXElements(content) {
  let modified = false;
  let fixCount = 0;
  
  // Fix malformed JSX opening tags
  content = content.replace(/<(\w+)([^>]*?)\s+>/g, (match, tagName, attrs) => {
    if (attrs.trim() && !attrs.endsWith(' ')) {
      modified = true;
      fixCount++;
      return `<${tagName}${attrs}>`;
    }
    return match;
  });
  
  // Fix malformed JSX self-closing tags
  content = content.replace(/<(\w+)([^>]*?)\s+\/>/g, (match, tagName, attrs) => {
    modified = true;
    fixCount++;
    return `<${tagName}${attrs} />`;
  });
  
  // Fix malformed JSX closing tags
  content = content.replace(/<\/(\w+)\s+>/g, (match, tagName) => {
    modified = true;
    fixCount++;
    return `</${tagName}>`;
  });
  
  // Fix broken JSX expressions
  content = content.replace(/\{\s*([^}]*?)\s*\}/g, (match, expr) => {
    if (expr.trim() && !expr.includes('{') && !expr.includes('}')) {
      return `{${expr.trim()}}`;
    }
    return match;
  });
  
  if (modified) {
    stats.jsxElementsFixed += fixCount;
  }
  
  return modified ? content : content;
}

/**
 * Fix missing commas - ',' expected
 */
function fixMissingCommas(content) {
  let modified = false;
  let fixCount = 0;
  
  // Fix missing commas in object literals
  content = content.replace(/(\w+:\s*[^,}\n]+)\s*\n\s*(\w+:)/g, (match, prop1, prop2) => {
    if (!prop1.trim().endsWith(',') && !prop1.trim().endsWith('{')) {
      modified = true;
      fixCount++;
      return `${prop1.trim()},\n  ${prop2}`;
    }
    return match;
  });
  
  // Fix missing commas in array literals
  content = content.replace(/(\w+)\s*\n\s*(\w+)/g, (match, item1, item2, offset, string) => {
    // Check if we're inside an array context
    const before = string.substring(0, offset);
    const lastBracket = Math.max(before.lastIndexOf('['), before.lastIndexOf(']'));
    const lastBrace = Math.max(before.lastIndexOf('{'), before.lastIndexOf('}'));
    
    if (lastBracket > lastBrace && before.substring(lastBracket).includes('[')) {
      if (!item1.trim().endsWith(',') && !item1.trim().endsWith('[')) {
        modified = true;
        fixCount++;
        return `${item1.trim()},\n  ${item2}`;
      }
    }
    return match;
  });
  
  // Fix missing commas in function parameters
  content = content.replace(/(\w+:\s*\w+)\s*\n\s*(\w+:)/g, (match, param1, param2) => {
    modified = true;
    fixCount++;
    return `${param1.trim()},\n  ${param2}`;
  });
  
  if (modified) {
    stats.commasFixed += fixCount;
  }
  
  return modified ? content : content;
}

/**
 * Fix unterminated string literals
 */
function fixUnterminatedStrings(content) {
  let modified = false;
  let fixCount = 0;
  
  // Fix unterminated double quotes
  content = content.replace(/="([^"]*?)$/gm, (match, value) => {
    modified = true;
    fixCount++;
    return `="${value}"`;
  });
  
  // Fix unterminated single quotes
  content = content.replace(/'([^']*?)$/gm, (match, value) => {
    modified = true;
    fixCount++;
    return `'${value}'`;
  });
  
  // Fix unterminated template literals
  content = content.replace(/`([^`]*?)$/gm, (match, value) => {
    modified = true;
    fixCount++;
    return `\`${value}\``;
  });
  
  if (modified) {
    stats.stringsFixed += fixCount;
  }
  
  return modified ? content : content;
}

/**
 * Fix expression issues
 */
function fixExpressions(content) {
  let modified = false;
  let fixCount = 0;
  
  // Fix malformed arrow functions
  content = content.replace(/=>\s*\{([^}]*?)\s*$/gm, (match, body) => {
    modified = true;
    fixCount++;
    return `=> {\n  ${body.trim()}\n}`;
  });
  
  // Fix incomplete expressions
  content = content.replace(/\{\s*([^}]*?)\s*$/gm, (match, expr) => {
    if (expr.trim() && !expr.includes('\n')) {
      modified = true;
      fixCount++;
      return `{${expr.trim()}}`;
    }
    return match;
  });
  
  if (modified) {
    stats.expressionsFixed += fixCount;
  }
  
  return modified ? content : content;
}

/**
 * Process a single file
 */
function processFile(filePath) {
  try {
    stats.filesProcessed++;
    
    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    
    // Apply fixes in order
    content = fixJSXElements(content);
    content = fixMissingCommas(content);
    content = fixUnterminatedStrings(content);
    content = fixExpressions(content);
    
    // Write back if modified
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      stats.filesModified++;
      console.log(`✅ Fixed: ${path.relative(rootDir, filePath)}`);
    }
    
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    stats.errors++;
  }
}

/**
 * Main execution
 */
function main() {
  console.log('🚀 Starting parsing error fixes...');
  console.log('📁 Source directory:', srcDir);
  console.log('');
  
  const files = findFiles(srcDir);
  console.log(`📄 Found ${files.length} files to check`);
  console.log('');
  
  files.forEach(processFile);
  
  console.log('');
  console.log('📊 SUMMARY:');
  console.log(`   Files processed: ${stats.filesProcessed}`);
  console.log(`   Files modified: ${stats.filesModified}`);
  console.log(`   JSX elements fixed: ${stats.jsxElementsFixed}`);
  console.log(`   Missing commas fixed: ${stats.commasFixed}`);
  console.log(`   Unterminated strings fixed: ${stats.stringsFixed}`);
  console.log(`   Expressions fixed: ${stats.expressionsFixed}`);
  console.log(`   Errors: ${stats.errors}`);
  console.log('');
  
  if (stats.errors === 0) {
    console.log('✅ All fixes completed successfully!');
  } else {
    console.log('⚠️  Some errors occurred during processing.');
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main, processFile };
