import React from 'react';
import Link from 'next/link';
import { UserRole } from '../../../types/roles';
import ApplyForOrganizerButton from './ApplyForOrganizerButton';
import { getUserApplication } from './apply/actions';
import { createClient } from '@/lib/supabase/pages-client';
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import { getAuthUser } from '@/lib/auth-utils';
import { logger } from '@/lib/logger';

export default async function OrganizationsPage() {
  if (process.env.NODE_ENV === 'development') {

    logger.debug('[DEBUG] OrganizationsPage - Initializing');

  }
  try {
    // Check if we have Supabase auth cookies - this is a quick check before doing any DB calls
    const cookieStore = await cookies();
    const hasAuthCookies = cookieStore.getAll().some(cookie =>
      cookie.name.includes('auth-token') || cookie.name.includes('supabase-auth')
    );

    if (process.env.NODE_ENV === 'development') {


      logger.debug('[DEBUG] OrganizationsPage - Has auth cookies:', hasAuthCookies);


    }
    if (!hasAuthCookies) {
      if (process.env.NODE_ENV === 'development') {

        logger.debug('[DEBUG] OrganizationsPage - No auth cookies found, redirecting to sign-in');

      }
      // If no auth cookies, redirect to sign-in
      redirect('/sign-in');
    }

    // First check if we have a user from the auth context
    // This should be available if middleware has already verified authentication
    const authUser = await getAuthUser();

    // Create Supabase client
    const supabase = await createClient();

    // If no auth user from cache, try to get from session
    let userId: string;

    if (!authUser) {
      if (process.env.NODE_ENV === 'development') {

        logger.debug('[DEBUG] OrganizationsPage - No auth user found, checking session');

      }
      // Double-check with session as fallback
      const { data: { session }, error } = await supabase.auth.getSession();

      if (error) {
        console.error('[DEBUG] OrganizationsPage - Error getting session:', error);
      }

      if (!session || !session.user) {
        if (process.env.NODE_ENV === 'development') {

          logger.debug('[DEBUG] OrganizationsPage - No session found, but has auth cookies. Using fallback UI');

        }
        // We have auth cookies but no session - this is likely a race condition
        // Instead of redirecting, show a loading state
        return (
          <div className="container mx-auto px-4 py-8">
            <h1 className="text-3xl font-bold mb-2">Organizations</h1>
            <p className="text-[hsl(var(--muted-foreground))] mb-6">Loading your organizations...</p>
            <div className="animate-pulse bg-[hsl(var(--muted))] dark:bg-[hsl(var(--dark-muted))] h-32 rounded-lg"></div>
          </div>
        );
      }

      userId = session.user.id;
      if (process.env.NODE_ENV === 'development') {

        logger.debug('[DEBUG] OrganizationsPage - Got user ID from session:', userId);

      }
    } else {
      userId = authUser.id;
      if (process.env.NODE_ENV === 'development') {

        logger.debug('[DEBUG] OrganizationsPage - Got user ID from auth context:', userId);

      }
    }

    // Check if user is an event organizer
    const { data: { user } } = await supabase.auth.getUser();
    const metadata = user?.user_metadata as { role?: UserRole } || {};
    const isEventOrganizer = metadata.role === UserRole.EVENT_ORGANIZER;
    if (process.env.NODE_ENV === 'development') {

      logger.debug('[DEBUG] OrganizationsPage - User is event organizer:', isEventOrganizer);

    }
    // Check for incomplete application
    if (process.env.NODE_ENV === 'development') {

      logger.debug('[DEBUG] OrganizationsPage - Checking for incomplete application');

    }
    const application = await getUserApplication(userId);
    const hasIncompleteApplication = application && application.status === 'draft';
    if (process.env.NODE_ENV === 'development') {

      logger.debug('[DEBUG] OrganizationsPage - Has incomplete application:', hasIncompleteApplication);

    }
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-2">Organizations</h1>
        <p className="text-[hsl(var(--muted-foreground))] mb-6">Manage your organizations and memberships</p>

        {/* Incomplete Application Notice */}
        {hasIncompleteApplication && (
          <div className="mb-6 p-4 bg-yellow-50 dark:bg-yellow-950/30 border border-yellow-200 dark:border-yellow-800/50 rounded-lg">
            <h3 className="text-lg font-medium text-yellow-800 dark:text-yellow-400 mb-2">
              Continue Your Application
            </h3>
            <p className="text-yellow-700 dark:text-yellow-500 mb-4">
              You have an incomplete event organizer application. Would you like to continue where you left off?
            </p>
            <Link
              href="/dashboard/organizations/apply"
              className="inline-block px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 dark:bg-yellow-700 dark:hover:bg-yellow-600 transition"
            >
              Continue Application
            </Link>
          </div>
        )}

        {isEventOrganizer ? (
          // Content for event organizers
          <div className="mb-8">
            <div className="bg-[hsl(var(--card))] text-[hsl(var(--card-foreground))] rounded-lg shadow border border-[hsl(var(--border))] p-6">
              <h2 className="text-xl font-semibold mb-4">Your Organizations</h2>

              {/* Organizations listing */}
              <div className="border border-[hsl(var(--border))] rounded-lg overflow-hidden">
                <div className="p-8 text-center text-[hsl(var(--muted-foreground))]">
                  <p>You don't have any organizations yet.</p>
                  <button className="mt-4 px-4 py-2 bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))] rounded-md hover:bg-[hsl(var(--primary-hover))] transition">
                    Create Organization
                  </button>
                </div>
              </div>
            </div>
          </div>
        ) : (
          // Content for regular users - Promotion CTA
          <div className="mb-8">
            <div className="bg-[hsl(var(--card))] text-[hsl(var(--card-foreground))] rounded-lg shadow border border-[hsl(var(--border))] p-6">
              <h2 className="text-xl font-semibold mb-4">Become an Event Organizer</h2>
              <div className="p-6 border border-[hsl(var(--primary-100))] bg-[hsl(var(--primary-50))] dark:bg-[hsl(var(--primary-subtle))] dark:border-[hsl(var(--primary-200))] rounded-lg">
                <h3 className="text-lg font-medium text-[hsl(var(--primary))] mb-2">Unlock Event Organizer Features</h3>
                <p className="text-[hsl(var(--foreground))] mb-4">
                  Apply to become an event organizer to create and manage your own events on our platform.
                  As an event organizer, you can:
                </p>
                <ul className="list-disc ml-5 text-[hsl(var(--foreground))] mb-4">
                  <li>Create and publish your own events</li>
                  <li>Manage ticket sales and attendees</li>
                  <li>Access detailed analytics and reporting</li>
                  <li>Build your brand with a customized organizer profile</li>
                </ul>
                <div className="mt-4">
                  <ApplyForOrganizerButton />
                </div>
              </div>
            </div>
          </div>
        )}

        {isEventOrganizer && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2 bg-[hsl(var(--card))] text-[hsl(var(--card-foreground))] rounded-lg shadow border border-[hsl(var(--border))] p-6">
              <h2 className="text-xl font-semibold mb-4">Create an Organization</h2>
              <p className="text-[hsl(var(--muted-foreground))] mb-4">
                Organizations help you collaborate with team members and manage events together.
              </p>

              <form className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-[hsl(var(--foreground))] mb-1">
                    Organization Name
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-[hsl(var(--input))] bg-[hsl(var(--background))] text-[hsl(var(--foreground))] rounded-md focus:border-[hsl(var(--primary))] focus:ring-[hsl(var(--ring))]"
                    placeholder="Enter organization name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-[hsl(var(--foreground))] mb-1">
                    Organization Type
                  </label>
                  <select className="w-full px-3 py-2 border border-[hsl(var(--input))] bg-[hsl(var(--background))] text-[hsl(var(--foreground))] rounded-md focus:border-[hsl(var(--primary))] focus:ring-[hsl(var(--ring))]">
                    <option value="">Select type</option>
                    <option value="business">Business</option>
                    <option value="community">Community</option>
                    <option value="nonprofit">Non-profit</option>
                    <option value="education">Education</option>
                  </select>
                </div>
                <button
                  type="submit"
                  className="px-4 py-2 bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))] rounded-md hover:bg-[hsl(var(--primary-hover))] transition"
                >
                  Create Organization
                </button>
              </form>
            </div>

            <div className="bg-[hsl(var(--card))] text-[hsl(var(--card-foreground))] rounded-lg shadow border border-[hsl(var(--border))] p-6">
              <h2 className="text-xl font-semibold mb-4">Join an Organization</h2>
              <p className="text-[hsl(var(--muted-foreground))] mb-4">
                Join an existing organization using an invitation code.
              </p>

              <form className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-[hsl(var(--foreground))] mb-1">
                    Invitation Code
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-[hsl(var(--input))] bg-[hsl(var(--background))] text-[hsl(var(--foreground))] rounded-md focus:border-[hsl(var(--primary))] focus:ring-[hsl(var(--ring))]"
                    placeholder="Enter invitation code"
                  />
                </div>
                <button
                  type="submit"
                  className="px-4 py-2 bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))] rounded-md hover:bg-[hsl(var(--primary-hover))] transition"
                >
                  Join Organization
                </button>
              </form>
            </div>
          </div>
        )}
      </div>
    );
  } catch (error) {
    // Handle any errors that occur during page rendering
    console.error('[DEBUG] OrganizationsPage - Error rendering page:', error);

    // Return a fallback UI
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-2">Organizations</h1>
        <p className="text-[hsl(var(--muted-foreground))] mb-6">Manage your organizations and memberships</p>

        <div className="bg-[hsl(var(--card))] text-[hsl(var(--card-foreground))] rounded-lg shadow border border-[hsl(var(--border))] p-6">
          <h2 className="text-xl font-semibold text-[hsl(var(--destructive))] mb-4">Unable to load organizations</h2>
          <p className="text-[hsl(var(--muted-foreground))]">
            We encountered an error while loading your organizations. Please try refreshing the page.
          </p>
        </div>
      </div>
    );
  }
}