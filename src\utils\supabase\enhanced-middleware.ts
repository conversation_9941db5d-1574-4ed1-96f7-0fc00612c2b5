import { createServer<PERSON>lient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'
import { copyCookiesFromRequest, copySupabaseAuthCookies } from '@/lib/cookie-utils'
import { logger } from '@/lib/logger';

/**
 * Update the session in the middleware with enhanced cookie handling
 * This function should be called at the beginning of the middleware function
 * to ensure the session is refreshed and cookies are properly handled
 *
 * @param request The Next.js request object
 * @returns A Next.js response object with updated cookies
 */
export async function updateSessionEnhanced(request: NextRequest) {
  // Create a response object that we can modify
  const supabaseResponse = NextResponse.next({
    request: {
      headers: request.headers,
    },
  })

  // Check if we have a no_redirect parameter to prevent redirect loops
  const url = new URL(request.url);
  const noRedirect = url.searchParams.get('no_redirect') === 'true';

  // Get the redirect count from headers or initialize it
  const redirectCount = parseInt(request.headers.get('x-redirect-count') || '0');

  // Check for middleware prefetch (Next.js internal)
  const isMiddlewarePrefetch = request.headers.get('x-middleware-prefetch') === '1';

  // Set request headers for tracking state
  const requestHeaders = new Headers(request.headers);

  // If we have no_redirect=true, add a header to track this
  if (noRedirect) {
    requestHeaders.set('x-no-redirect', 'true');
  }

  // If we have a high redirect count, also set the no-redirect header
  if (redirectCount > 2) {
    requestHeaders.set('x-no-redirect', 'true');
    requestHeaders.set('x-high-redirect-count', 'true');
  }

  // If this is a middleware prefetch, mark it as a background refresh
  if (isMiddlewarePrefetch) {
    requestHeaders.set('x-background-refresh', 'true');
  }

  // Check for reset-complete cookie to handle auth reset
  const resetComplete = request.cookies.get('sb-reset-complete');
  if (resetComplete) {
    requestHeaders.set('x-auth-reset-complete', 'true');
  }

  // Log cookies for debugging
  if (process.env.NODE_ENV === 'development') {
    const cookieNames = request.cookies.getAll().map(c => c.name);
    logger.info('Middleware cookies before client creation:', cookieNames);
  }

  // Create a Supabase client for the middleware
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          // Log cookie operations in development
          if (process.env.NODE_ENV === 'development' && cookiesToSet.length > 0) {
            logger.info('Middleware setting cookies:', cookiesToSet.map(c => c.name));
          }

          // Set cookies on the response
          for (const { name, value, options } of cookiesToSet) {
            supabaseResponse.cookies.set(name, value, options)
          }
        },
      },
    }
  )

  try {
    // Check if this is a background refresh request
    const isBackgroundRefresh = url.searchParams.has('no_redirect') ||
      requestHeaders.get('x-background-refresh') === 'true' ||
      requestHeaders.get('x-middleware-prefetch') === '1' ||
      redirectCount > 2;

    // Add a header to track background refreshes
    if (isBackgroundRefresh) {
      requestHeaders.set('x-background-refresh', 'true');
      supabaseResponse.headers.set('x-background-refresh', 'true');

      // Add no_redirect=true to the URL if it's not already there
      if (!url.searchParams.has('no_redirect')) {
        const currentUrl = new URL(request.url);
        currentUrl.searchParams.set('no_redirect', 'true');

        // We don't redirect here, but we update the URL in the headers
        supabaseResponse.headers.set('x-original-url', currentUrl.toString());
      }
    }

    // Track the redirect count in the headers
    requestHeaders.set('x-redirect-count', redirectCount.toString());
    supabaseResponse.headers.set('x-redirect-count', redirectCount.toString());

    // Always use getUser() first for security - it validates the JWT
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    // Only refresh the session if this is not a background refresh
    if (!isBackgroundRefresh && user && !userError) {
      // Get the session to ensure it's refreshed
      await supabase.auth.getSession();
    }

    // Log session state in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Enhanced middleware updateSession:', {
        hasUser: !!user,
        errorMessage: userError ? userError.message : null,
        url: request.url,
        isBackgroundRefresh
      });
    }

    // Create a new response with the updated headers
    const enhancedResponse = NextResponse.next({
      request: {
        headers: requestHeaders,
      },
    });

    // Copy all cookies from the Supabase response to the enhanced response
    copySupabaseAuthCookies(supabaseResponse, enhancedResponse);

    // Add security headers
    enhancedResponse.headers.set('X-Frame-Options', 'DENY');
    enhancedResponse.headers.set('X-Content-Type-Options', 'nosniff');
    enhancedResponse.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
    enhancedResponse.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=(), interest-cohort=()');

    if (userError) {
      throw new Error(userError.message ?? 'An error occurred while fetching user data');
    }

    return enhancedResponse;
  } catch (error) {
    console.error('Error in updateSessionEnhanced:', error);

    // If there's an error, we still want to return a valid response
    return supabaseResponse;
  }
}
