/**
 * Role and permission type definitions
 * @module types/auth/roles
 */

/**
 * Available user roles
 */
export enum UserRole {
    SUPER_ADMIN = 'super_admin',
    ADMIN = 'admin',
    ORGANIZER = 'organizer',
    STAFF = 'staff',
    USER = 'user',
}

/**
 * Available permissions
 */
export enum Permission {
    // Event permissions
    CREATE_EVENT = 'create:event',
    READ_EVENT = 'read:event',
    UPDATE_EVENT = 'update:event',
    DELETE_EVENT = 'delete:event',
    PUBLISH_EVENT = 'publish:event',

    // Category permissions
    CREATE_CATEGORY = 'create:category',
    READ_CATEGORY = 'read:category',
    UPDATE_CATEGORY = 'update:category',
    DELETE_CATEGORY = 'delete:category',

    // Registration permissions
    CREATE_REGISTRATION = 'create:registration',
    READ_REGISTRATION = 'read:registration',
    UPDATE_REGISTRATION = 'update:registration',
    DELETE_REGISTRATION = 'delete:registration',
    APPROVE_REGISTRATION = 'approve:registration',

    // User permissions
    CREATE_USER = 'create:user',
    READ_USER = 'read:user',
    UPDATE_USER = 'update:user',
    DELETE_USER = 'delete:user',
    ASSIGN_ROLE = 'assign:role',

    // Organization permissions
    CREATE_ORGANIZATION = 'create:organization',
    READ_ORGANIZATION = 'read:organization',
    UPDATE_ORGANIZATION = 'update:organization',
    DELETE_ORGANIZATION = 'delete:organization',
}

/**
 * Role definition with associated permissions
 */
export interface Role {
    /** Role name */
    name: UserRole;
    /** Role description */
    description: string;
    /** Associated permissions */
    permissions: Permission[];
}

/**
 * User role assignment
 */
export interface UserRoleAssignment {
    /** User ID */
    userId: string;
    /** Assigned role */
    role: UserRole;
    /** Organization ID (if role is organization-specific) */
    organizationId?: string;
    /** Creation timestamp */
    createdAt: Date;
    /** Last update timestamp */
    updatedAt: Date;
}

/**
 * Default role configurations
 */
export const DEFAULT_ROLES: Record<UserRole, Role> = {
    [UserRole.SUPER_ADMIN]: {
        name: UserRole.SUPER_ADMIN,
        description: 'Full system access',
        permissions: Object.values(Permission),
    },
    [UserRole.ADMIN]: {
        name: UserRole.ADMIN,
        description: 'Organization administrator',
        permissions: [
            Permission.CREATE_EVENT,
            Permission.READ_EVENT,
            Permission.UPDATE_EVENT,
            Permission.DELETE_EVENT,
            Permission.PUBLISH_EVENT,
            Permission.CREATE_CATEGORY,
            Permission.READ_CATEGORY,
            Permission.UPDATE_CATEGORY,
            Permission.DELETE_CATEGORY,
            Permission.CREATE_REGISTRATION,
            Permission.READ_REGISTRATION,
            Permission.UPDATE_REGISTRATION,
            Permission.DELETE_REGISTRATION,
            Permission.APPROVE_REGISTRATION,
            Permission.CREATE_USER,
            Permission.READ_USER,
            Permission.UPDATE_USER,
            Permission.ASSIGN_ROLE,
        ],
    },
    [UserRole.ORGANIZER]: {
        name: UserRole.ORGANIZER,
        description: 'Event organizer',
        permissions: [
            Permission.CREATE_EVENT,
            Permission.READ_EVENT,
            Permission.UPDATE_EVENT,
            Permission.CREATE_CATEGORY,
            Permission.READ_CATEGORY,
            Permission.UPDATE_CATEGORY,
            Permission.READ_REGISTRATION,
            Permission.UPDATE_REGISTRATION,
            Permission.APPROVE_REGISTRATION,
        ],
    },
    [UserRole.STAFF]: {
        name: UserRole.STAFF,
        description: 'Event staff',
        permissions: [
            Permission.READ_EVENT,
            Permission.READ_CATEGORY,
            Permission.READ_REGISTRATION,
            Permission.UPDATE_REGISTRATION,
        ],
    },
    [UserRole.USER]: {
        name: UserRole.USER,
        description: 'Regular user',
        permissions: [
            Permission.READ_EVENT,
            Permission.READ_CATEGORY,
            Permission.CREATE_REGISTRATION,
            Permission.READ_REGISTRATION,
        ],
    },
}; 