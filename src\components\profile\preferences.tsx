'use client'

import { useState } from 'react'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { toast } from '@/components/ui/use-toast'
import { User } from '@/types/auth'

interface PreferencesProps {
    user: User
}

export function Preferences({ user }: PreferencesProps) {
    const [emailNotifications, setEmailNotifications] = useState(true)
    const [pushNotifications, setPushNotifications] = useState(true)
    const [marketingEmails, setMarketingEmails] = useState(false)
    const [loading, setLoading] = useState(false)

    const handleSave = async () => {
        setLoading(true)
        try {
            // Save preferences to backend
            toast({
                title: 'Success',
                description: 'Your preferences have been saved.',
                variant: 'success',
            })
        } catch (error) {
            console.error('Error saving preferences:', error)
            toast({
                title: 'Error',
                description: 'Failed to save preferences. Please try again.',
                variant: 'destructive',
            })
        } finally {
            setLoading(false)
        }
    }

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                    <Label>Email Notifications</Label>
                    <p className="text-sm text-muted-foreground">
                        Receive notifications about your events via email
                    </p>
                </div>
                <Switch
                    checked={emailNotifications}
                    onCheckedChange={setEmailNotifications}
                    disabled={loading}
                />
            </div>

            <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                    <Label>Push Notifications</Label>
                    <p className="text-sm text-muted-foreground">
                        Receive push notifications in your browser
                    </p>
                </div>
                <Switch
                    checked={pushNotifications}
                    onCheckedChange={setPushNotifications}
                    disabled={loading}
                />
            </div>

            <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                    <Label>Marketing Emails</Label>
                    <p className="text-sm text-muted-foreground">
                        Receive emails about new features and updates
                    </p>
                </div>
                <Switch
                    checked={marketingEmails}
                    onCheckedChange={setMarketingEmails}
                    disabled={loading}
                />
            </div>

            <Button
                onClick={handleSave}
                disabled={loading}
                className="w-full"
            >
                {loading ? 'Saving...' : 'Save Preferences'}
            </Button>
        </div>
    )
} 