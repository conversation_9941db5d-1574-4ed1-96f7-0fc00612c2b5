import { FieldType } from './field-base';
import type {
    EventField,
    TextField,
    NumberField,
    SelectField,
    MultiSelectField,
    DateField,
    BooleanField
} from './field-types';

export function isTextField(field: EventField): field is TextField {
    return field.type === FieldType.Text;
}

export function isNumberField(field: EventField): field is NumberField {
    return field.type === FieldType.Number;
}

export function isSelectField(field: EventField): field is SelectField {
    return field.type === FieldType.Select;
}

export function isMultiSelectField(field: EventField): field is MultiSelectField {
    return field.type === FieldType.MultiSelect;
}

export function isDateField(field: EventField): field is DateField {
    return field.type === FieldType.Date;
}

export function isBooleanField(field: EventField): field is BooleanField {
    return field.type === FieldType.Boolean;
}

export function hasOptions(field: EventField): field is SelectField | MultiSelectField {
    return isSelectField(field) || isMultiSelectField(field);
}

export function getDisplayValue(field: EventField, value: unknown): string {
    if (value === null || value === undefined) {
        return '';
    }

    if (hasOptions(field)) {
        if (isMultiSelectField(field) && Array.isArray(value)) {
            return value
                .map(v => field.options.find(opt => opt.value === v)?.label || v)
                .join(', ');
        }
        const option = field.options.find(opt => opt.value === value);
        return option?.label || String(value);
    }

    if (isDateField(field) && value instanceof Date) {
        return value.toLocaleDateString();
    }

    return String(value);
} 