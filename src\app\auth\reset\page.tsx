'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { CheckCircle, AlertCircle } from 'lucide-react';

export default function AuthResetPage() {
  const [isResetting, setIsResetting] = useState(false);
  const [result, setResult] = useState<{ success: boolean; message: string; details?: unknown } | null>(null);

  const handleReset = async () => {
    try {
      setIsResetting(true);
      setResult(null);

      // Clear local storage
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('sb-') || key.includes('supabase')) {
          localStorage.removeItem(key);
        }
      });

      // Clear session storage
      Object.keys(sessionStorage).forEach(key => {
        if (key.startsWith('sb-') || key.includes('supabase')) {
          sessionStorage.removeItem(key);
        }
      });

      // Call the server-side API to clear cookies
      const response = await fetch('/api/auth/reset-state', {
        method: 'POST',
        credentials: 'include', // Important for cookies
      });

      const data = await response.json();

      setResult({
        success: data.success,
        message: data.success ? 'Authentication state reset successfully' : 'Failed to reset authentication state',
        details: data
      });
    } catch (error) {
      console.error('Error resetting auth state:', error);
      setResult({
        success: false,
        message: 'An error occurred while resetting authentication state',
        details: error instanceof Error ? error.message : String(error)
      });
    } finally {
      setIsResetting(false);
    }
  };

  const handleSignIn = () => {
    window.location.href = '/sign-in?reset_auth=true';
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-background">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Reset Authentication State</CardTitle>
          <CardDescription>
            Use this page to reset your authentication state if you're experiencing issues with sign-in or sign-out.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {result && (
            <Alert variant={result.success ? "default" : "destructive"}>
              <div className="flex items-center gap-2">
                {result.success ? <CheckCircle className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />}
                <AlertTitle>{result.success ? 'Success' : 'Error'}</AlertTitle>
              </div>
              <AlertDescription className="mt-2">
                {result.message}
                {result.details && (
                  <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-auto">
                    {JSON.stringify(result.details, null, 2)}
                  </pre>
                )}
              </AlertDescription>
            </Alert>
          )}

          <div className="text-sm text-muted-foreground">
            <p>This will:</p>
            <ul className="list-disc pl-5 mt-2 space-y-1">
              <li>Clear all Supabase authentication cookies</li>
              <li>Remove Supabase-related items from local storage</li>
              <li>Remove Supabase-related items from session storage</li>
            </ul>
          </div>
        </CardContent>
        <CardFooter className="flex flex-col sm:flex-row gap-2">
          <Button
            onClick={handleReset}
            disabled={isResetting}
            className="w-full"
          >
            {isResetting ? 'Resetting...' : 'Reset Authentication State'}
          </Button>
          <Button
            onClick={handleSignIn}
            variant="outline"
            className="w-full"
          >
            Go to Sign In
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
