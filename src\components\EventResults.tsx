'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Calendar, MapPin, Loader2 } from 'lucide-react';
import { EventCategory, MalaysiaState } from '../types/event';
import { formatDate } from '../utils/formatDate';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { usePublicEvents } from '@/hooks/use-public-events';
import { Event } from '@/repositories/event-repository';

interface EventResultsProps {
  keyword: string;
  category: EventCategory | null;
  state: MalaysiaState | null;
  className?: string;
}

export default function EventResults({
  keyword = '',
  category,
  state,
  className = '',
}: EventResultsProps) {
  // Use the public events hook with category and state filters
  const categoryStr = category ? category.toString() : undefined;
  const stateStr = state ? state.toString() : undefined;
  const { events, loading, error } = usePublicEvents(categoryStr, stateStr);
  const [filteredEvents, setFilteredEvents] = useState<Event[]>([]);
  const [imageErrors, setImageErrors] = useState<{ [key: string]: boolean }>({});

  const fallbackImage = '/images/fallback/fallback-default.svg';

  useEffect(() => {
    if (!events || events.length === 0) return;

    // Filter events based on keyword (category and state are already filtered by the API)
    let results = [...events];

    if (keyword) {
      const lowercaseKeyword = keyword.toLowerCase();
      results = results.filter(event =>
        (event.title?.toLowerCase().includes(lowercaseKeyword) || false) ||
        (event.description?.toLowerCase().includes(lowercaseKeyword) || false) ||
        (event.location?.toLowerCase().includes(lowercaseKeyword) || false)
      );
    }

    setFilteredEvents(results);
  }, [events, keyword]);

  const handleImageError = (url: string) => {
    setImageErrors(prev => ({
      ...prev,
      [url]: true
    }));
  };

  const getImageSrc = (url: string) => {
    return imageErrors[url] ? fallbackImage : url;
  };

  if (loading) {
    return (
      <div className={`flex flex-col items-center justify-center py-16 ${className}`}>
        <div className="flex items-center justify-center mb-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
        <p className="text-muted-foreground text-center">
          Loading events...
        </p>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`flex flex-col items-center justify-center py-16 ${className}`}>
        <h3 className="text-xl font-medium mb-2 text-red-500">Error loading events</h3>
        <p className="text-muted-foreground text-center">
          {error.message || "Something went wrong. Please try again later."}
        </p>
      </div>
    );
  }

  if (filteredEvents.length === 0) {
    return (
      <div className={`flex flex-col items-center justify-center py-16 ${className}`}>
        <h3 className="text-xl font-medium mb-2">No events found</h3>
        <p className="text-muted-foreground text-center">
          Try adjusting your search filters to find what you&#39;re looking for.
        </p>
      </div>
    );
  }

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}>
      {filteredEvents.map((event) => (
        <Link href={`/events/${event.slug || event.id}`} key={event.id}>
          <Card className="h-full hover:shadow-md transition-shadow duration-200">
            <div className="relative h-48 w-full overflow-hidden rounded-t-lg">
              <Image
                src={getImageSrc(event.coverImage?.url || event.posterImage?.url || fallbackImage)}
                alt={event.title || 'Event'}
                fill
                className="object-cover"
                onError={() => handleImageError(event.coverImage?.url || event.posterImage?.url || '')}
              />
              <Badge className="absolute top-2 right-2 z-10">
                {event.eventTypeId || 'Event'}
              </Badge>
            </div>

            <CardContent className="pt-4">
              <h3 className="text-lg font-semibold line-clamp-2 mb-2">{event.title}</h3>
              <p className="text-muted-foreground line-clamp-3 mb-4">{event.description || 'No description available'}</p>

              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Calendar className="h-4 w-4" />
                <span>{formatDate(event.startDate || '')}</span>
              </div>

              <div className="flex items-center gap-2 text-sm text-muted-foreground mt-1">
                <MapPin className="h-4 w-4" />
                <span className="line-clamp-1">{event.location}, {event.state}</span>
              </div>
            </CardContent>

            <CardFooter className="border-t pt-4">
              <div className="flex justify-between items-center w-full">
                <div className="text-sm">
                  {/* Price information might be in categories */}
                  Free
                </div>
                <Badge variant="outline" className="ml-auto">
                  View Details
                </Badge>
              </div>
            </CardFooter>
          </Card>
        </Link>
      ))}
    </div>
  );
}