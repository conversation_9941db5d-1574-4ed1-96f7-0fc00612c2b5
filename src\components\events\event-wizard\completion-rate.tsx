'use client';

import { useW<PERSON>rd, WizardStep } from './wizard-container';
import { Progress } from '@/components/ui/progress';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Info } from 'lucide-react';
import { FormData } from '@/lib/validations/event-schema';

interface RequiredField {
  name: keyof FormData;
  label: string;
  weight: number;
  isArray?: boolean;
}

export function calculateCompletionRate(formData: FormData): { rate: number; completed: string[]; missing: string[] } {
  // Define required fields for a complete event
  const requiredFields: RequiredField[] = [
    { name: 'eventTypeId', label: 'Event Type', weight: 1 },
    { name: 'title', label: 'Title', weight: 1 },
    { name: 'description', label: 'Description', weight: 1 },
    { name: 'location', label: 'Location', weight: 1 },
    { name: 'country', label: 'Country', weight: 1 },
    { name: 'state', label: 'State', weight: 1 },
    { name: 'city', label: 'City', weight: 1 },
    { name: 'startDate', label: 'Start Date', weight: 1 },
    { name: 'endDate', label: 'End Date', weight: 1 },
    { name: 'timezone', label: 'Timezone', weight: 0.5 },
    { name: 'categories', label: 'Categories', weight: 1, isArray: true },
    { name: 'customFields', label: 'Custom Fields', weight: 1, isArray: true },
    { name: 'posterImage', label: 'Poster Image', weight: 1.5 },
    { name: 'coverImage', label: 'Cover Image', weight: 1.5 },
  ];

  // Calculate total possible weight
  const totalWeight = requiredFields.reduce((sum, field) => sum + field.weight, 0);

  // Calculate completed weight
  let completedWeight = 0;
  const completed: string[] = [];
  const missing: string[] = [];

  requiredFields.forEach(field => {
    const value = formData[field.name];
    const isCompleted = field.isArray
      ? Array.isArray(value) && value.length > 0
      : value !== undefined && value !== null && value !== '';

    if (isCompleted) {
      completedWeight += field.weight;
      completed.push(field.label);
    } else {
      missing.push(field.label);
    }
  });

  // Calculate completion rate as a percentage
  const rate = Math.round((completedWeight / totalWeight) * 100);

  return { rate, completed, missing };
}

interface CompletionRateIndicatorProps {
  step: number;
}

export function CompletionRateIndicator({ step }: CompletionRateIndicatorProps) {
  const totalSteps = Object.keys(WizardStep).length / 2;
  const completionRate = Math.round((step / (totalSteps - 1)) * 100);

  return (
    <div className="flex items-center gap-2">
      <div className="h-2 w-24 bg-[hsl(var(--muted))] rounded-full overflow-hidden">
        <div
          className="h-full bg-[hsl(var(--primary))] transition-all duration-300"
          style={{ width: `${completionRate}%` }}
        />
      </div>
      <span className="text-sm text-[hsl(var(--muted-foreground))]">
        {completionRate}%
      </span>
    </div>
  );
}
