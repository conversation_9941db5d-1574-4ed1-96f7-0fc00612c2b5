import { createClient } from '@/lib/supabase/pages-client'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

export const dynamic = 'force-dynamic'

/**
 * API endpoint to check the current session state
 * This is useful for debugging authentication issues
 */
export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = await createClient()

    // Get the current session
    const { data: { session }, error } = await supabase.auth.getSession()

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // Get all cookies for debugging
    const allCookies = Array.from(cookieStore.getAll()).map(c => {
      return {
        name: c.name,
        hasValue: !!c.value,
        // Use 'in' operator to check for property existence before access
        ...('expires' in c && c.expires !== undefined && { expires: c.expires as number | undefined }),
        ...('path' in c && c.path !== undefined && { path: c.path as string | undefined }),
        ...('domain' in c && c.domain !== undefined && { domain: c.domain as string | undefined }),
        ...('secure' in c && c.secure !== undefined && { secure: c.secure as boolean | undefined }),
        ...('httpOnly' in c && c.httpOnly !== undefined && { httpOnly: c.httpOnly as boolean | undefined }),
        ...('sameSite' in c && c.sameSite !== undefined && { sameSite: c.sameSite as 'strict' | 'lax' | 'none' | undefined }),
      };
    })

    // Return session info
    return NextResponse.json({
      authenticated: !!session,
      sessionExists: !!session,
      user: session?.user ? {
        id: session.user.id,
        email: session.user.email,
        // Don't include sensitive data
      } : null,
      cookies: {
        count: allCookies.length,
        names: allCookies.map(c => c.name),
        details: allCookies
      },
      timestamp: new Date().toISOString(),
      // Include request info for debugging
      request: {
        url: request.url,
        method: request.method,
        headers: Object.fromEntries(
          Array.from(request.headers.entries())
            .filter(([key]) => !['cookie', 'authorization'].includes(key.toLowerCase()))
        ),
      }
    })
  } catch (err) {
    console.error('Error in session-check route:', err)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
