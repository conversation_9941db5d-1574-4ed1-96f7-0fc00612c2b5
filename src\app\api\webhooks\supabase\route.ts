import { createClient } from '@/lib/supabase/pages-client'
import { createAdminClient } from '@/lib/supabase/admin-client'
import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import * as crypto from 'crypto'
import { extractAvatarFromOAuth } from '@/utils/imageHandling'
import { logger } from '@/lib/logger'
import { SupabaseClient } from '@supabase/supabase-js'
import { Database } from '@/types/supabase'

export const dynamic = 'force-dynamic'

type WebhookPayload = {
  type: string
  data: {
    id: string
    email?: string
    user_metadata?: Record<string, unknown>
    email_confirmed_at?: string
  }
} & Record<string, unknown>

/**
 * Handle Supabase Auth webhooks
 * This route is called by Supabase Auth when certain events occur
 */
export async function POST(request: NextRequest) {
  // Get headers from request instead of headers()
  const signature = request.headers.get('x-signature') || ''
  const timestamp = request.headers.get('x-webhook-timestamp') || ''
  const webhookSecret = process.env.SUPABASE_WEBHOOK_SECRET

  if (!signature || !timestamp || !webhookSecret) {
    return NextResponse.json(
      { error: 'Missing required webhook headers' },
      { status: 400 }
    )
  }

  const payload = await request.json() as WebhookPayload
  const supabase = await createClient()
  const adminClient = await createAdminClient()

  try {
    // Verify webhook signature
    const signingString = `${timestamp}.${JSON.stringify(payload)}`
    const hmac = crypto.createHmac('sha256', webhookSecret)
    hmac.update(signingString)
    const hash = hmac.digest('hex')

    if (hash !== signature) {
      return NextResponse.json({ error: 'Invalid signature' }, { status: 401 })
    }

    // Log webhook payload
    const webhookLog: Database['public']['Tables']['webhook_logs']['Insert'] = {
      type: payload.type,
      payload: payload as unknown as Database['public']['Tables']['webhook_logs']['Insert']['payload']
    }
    const { error } = await adminClient.from('webhook_logs').insert(webhookLog)

    if (error) {
      logger.error('Error logging webhook:', error)
    }

    // Handle different webhook events
    switch (payload.type) {
      case 'user.created':
        // Handle user creation
        break
      case 'user.deleted':
        // Handle user deletion
        break
      default:
        logger.info(`Unhandled webhook type: ${payload.type}`)
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    logger.error('Error processing webhook:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
