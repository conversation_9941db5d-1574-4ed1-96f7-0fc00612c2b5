/**
 * Supabase admin client for privileged operations
 * This file provides a client that uses the service role key for admin operations
 * SECURITY WARNING: Never expose this client to the browser or client-side code
 */

import { createClient as createSupabaseClient } from '@supabase/supabase-js'
import { Database } from '@/types/supabase'

/**
 * Create a Supabase admin client with service role key for privileged operations
 * This should only be used on the server side and for operations that require admin privileges
 * SECURITY WARNING: Never expose this client to the browser or client-side code
 */
export const createAdminClient = async () => {
  const supabase = createSupabaseClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!
  )
  return supabase
}
