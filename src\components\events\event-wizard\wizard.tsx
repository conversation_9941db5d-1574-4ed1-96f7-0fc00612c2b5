/**
 * Event wizard component
 * @module components/events/event-wizard/wizard
 */

'use client';

import { useEffect } from 'react';
import { useWizard } from './wizard-container';
import { FormData } from '@/lib/validations/event-schema';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { FieldsStep } from './steps/fields-step';

interface Step {
    title: string;
    component: React.ComponentType;
}

const steps: Step[] = [
    {
        title: 'Event Details',
        component: () => <div>Details Step</div> // TODO: Implement details step
    },
    {
        title: 'Form Fields',
        component: FieldsStep
    },
    {
        title: 'Preview',
        component: () => <div>Preview Step</div> // TODO: Implement preview step
    }
];

export function EventWizard() {
    const { formData, currentStep, setCurrentStep } = useWizard();

    const handleNext = () => {
        if (currentStep < steps.length - 1) {
            setCurrentStep(currentStep + 1);
        }
    };

    const handleBack = () => {
        if (currentStep > 0) {
            setCurrentStep(currentStep - 1);
        }
    };

    const handleSubmit = async () => {
        // TODO: Implement form submission
        console.log('Form data:', formData);
    };

    const CurrentStepComponent = steps[currentStep]?.component || steps[0].component;

    return (
        <Card>
            <CardHeader>
                <CardTitle>{steps[currentStep]?.title || steps[0].title}</CardTitle>
            </CardHeader>
            <CardContent>
                <CurrentStepComponent />
            </CardContent>
            <CardFooter className="flex justify-between">
                <Button
                    variant="outline"
                    onClick={handleBack}
                    disabled={currentStep === 0}
                >
                    Back
                </Button>
                {currentStep === steps.length - 1 ? (
                    <Button onClick={handleSubmit}>
                        Submit
                    </Button>
                ) : (
                    <Button onClick={handleNext}>
                        Next
                    </Button>
                )}
            </CardFooter>
        </Card>
    );
} 