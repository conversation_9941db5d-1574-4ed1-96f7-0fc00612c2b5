/**
 * Supabase provider component
 * @module components/providers/supabase-provider
 */

'use client';

import { createContext, useContext, useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';
import type { SupabaseClient } from '@supabase/supabase-js';

interface SupabaseContextType {
    supabase: SupabaseClient | null;
}

const SupabaseContext = createContext<SupabaseContextType>({
    supabase: null,
});

export function useSupabase() {
    const context = useContext(SupabaseContext);
    if (!context) {
        throw new Error('useSupabase must be used within a SupabaseProvider');
    }
    return context;
}

interface SupabaseProviderProps {
    children: React.ReactNode;
}

export function SupabaseProvider({ children }: SupabaseProviderProps) {
    const [supabase, setSupabase] = useState<SupabaseClient | null>(null);

    useEffect(() => {
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
        const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

        if (!supabaseUrl || !supabaseAnonKey) {
            console.error('Supabase environment variables are not set');
            return;
        }

        const client = createClient(supabaseUrl, supabaseAnonKey);
        setSupabase(client);
    }, []);

    return (
        <SupabaseContext.Provider value={{ supabase }}>
            {children}
        </SupabaseContext.Provider>
    );
} 