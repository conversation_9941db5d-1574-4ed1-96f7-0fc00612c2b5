#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Safe fix for React unescaped entities
 * Only fixes quotes and apostrophes in JSX text content (not attributes)
 */

// Files that need unescaped entity fixes based on lint output
const targetFiles = [
    'src/app/(legal)/cookies/page.tsx',
    'src/app/(legal)/privacy-policy/page.tsx',
    'src/app/(legal)/terms/page.tsx',
    'src/app/about/page.tsx',
    'src/app/admin/migrations/registration-fields/page.tsx',
    'src/app/api/user/profile/route.ts',
    'src/app/dashboard/organizations/apply/OrganizerApplicationForm.tsx',
    'src/app/events/registration-success/page.tsx',
    'src/components/dashboard/header.tsx',
    'src/components/events/dashboard/event-actions.tsx',
    'src/components/profile/privacy-settings.tsx'
];

/**
 * Fix unescaped entities in JSX text content
 */
function fixUnescapedEntities(content) {
    const fixes = [];
    let result = content;

    // Look for text content between JSX tags that contains quotes or apostrophes
    // This is a safe pattern that only targets obvious text content
    const lines = result.split('\n');

    lines.forEach((line, index) => {
        // Skip lines that are obviously attributes (contain = before the quote)
        if (line.includes('=') && (line.includes('"') || line.includes("'"))) {
            return; // Skip attribute lines
        }

        // Only fix lines that look like JSX text content
        if (line.trim().match(/^[^<]*>[^<]*["'][^<]*</) ||
            line.trim().match(/^[^<]*["'][^<]*$/) && !line.includes('=')) {

            let fixedLine = line;
            let lineFixed = false;

            // Replace quotes in text content
            if (line.includes('"') && !line.includes('&quot;')) {
                // Only replace quotes that are clearly in text content, not attributes
                const quotesInText = line.replace(/="[^"]*"/g, '').includes('"');
                if (quotesInText) {
                    fixedLine = fixedLine.replace(/"/g, '&quot;');
                    lineFixed = true;
                }
            }

            // Replace apostrophes in text content  
            if (line.includes("'") && !line.includes('&apos;')) {
                // Only replace apostrophes that are clearly in text content, not attributes
                const apostrophesInText = line.replace(/='[^']*'/g, '').includes("'");
                if (apostrophesInText) {
                    fixedLine = fixedLine.replace(/'/g, '&apos;');
                    lineFixed = true;
                }
            }

            if (lineFixed) {
                lines[index] = fixedLine;
                fixes.push(`Fixed unescaped entities on line ${index + 1}`);
            }
        }
    });

    result = lines.join('\n');
    return { content: result, fixes };
}

/**
 * Process a single file
 */
function processFile(filePath) {
    try {
        if (!fs.existsSync(filePath)) {
            console.log(`⚠️  File not found: ${filePath}`);
            return false;
        }

        const content = fs.readFileSync(filePath, 'utf8');
        const { content: result, fixes } = fixUnescapedEntities(content);

        if (result !== content) {
            fs.writeFileSync(filePath, result, 'utf8');
            console.log(`✅ Fixed: ${path.relative(process.cwd(), filePath)}`);
            fixes.forEach(fix => console.log(`   - ${fix}`));
            return true;
        } else {
            console.log(`⚪ No changes needed: ${path.relative(process.cwd(), filePath)}`);
            return false;
        }
    } catch (error) {
        console.error(`❌ Error processing ${filePath}:`, error.message);
        return false;
    }
}

/**
 * Main execution
 */
function main() {
    console.log('🔧 Fixing unescaped entities in JSX text content...\n');

    let processedCount = 0;
    let modifiedCount = 0;

    targetFiles.forEach(relativePath => {
        const fullPath = path.join(process.cwd(), relativePath);
        processedCount++;

        if (processFile(fullPath)) {
            modifiedCount++;
        }
    });

    console.log('\n📊 Unescaped Entities Fix Summary:');
    console.log(`   Files processed: ${processedCount}`);
    console.log(`   Files fixed: ${modifiedCount}`);
    console.log(`   Files unchanged: ${processedCount - modifiedCount}`);

    if (modifiedCount > 0) {
        console.log('\n🎉 Unescaped entities fixed! Run "pnpm lint" to verify.');
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = { fixUnescapedEntities, processFile }; 