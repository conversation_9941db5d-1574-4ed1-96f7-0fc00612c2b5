import Image from 'next/image'
import { Metada<PERSON> } from 'next'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'

export const metadata: Metadata = {
  title: 'About Fuiyoo - Our Story, Mission, and Values',
  description: 'Learn about <PERSON><PERSON><PERSON><PERSON>, the team behind it, and our mission to revolutionize the event experience in Malaysia and beyond.',
}

const teamMembers = [
  {
    id: 1,
    name: '<PERSON><PERSON>',
    role: 'Founder & CEO',
    bio: '<PERSON><PERSON> is a visionary leader with a passion for community building and a knack for spotting emerging trends. She drives Fuiyoo&apos;s strategic direction.',
    imageUrl: 'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
  },
  {
    id: 2,
    name: '<PERSON>',
    role: 'Chief Technology Officer',
    bio: '<PERSON> is the architectural mastermind behind Fuiyoo&apos;s robust platform. He loves tackling complex challenges and brewing the perfect cup of coffee.',
    imageUrl: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
  },
  {
    id: 3,
    name: '<PERSON>',
    role: 'Head of Product',
    bio: '<PERSON> ensures Fuiyoo meets the evolving needs of our users. Her user-centric approach makes our platform intuitive and delightful.',
    imageUrl: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
  },
]

export default function AboutPage() {
  return (
    <main>
      <div className="container mx-auto px-4 py-12 md:py-20">
        {/* Hero Section */}
        <section className="text-center mb-16 md:mb-24">
          <h1 className="text-4xl md:text-5xl font-bold text-primary mb-4">
            About Fuiyoo
          </h1>
          <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto">
            Connecting communities and creating memorable experiences through innovative event technology.
          </p>
          <div className="mt-8 relative aspect-[16/7] rounded-xl overflow-hidden shadow-lg">
            <Image
              src="https://images.pexels.com/photos/3184325/pexels-photo-3184325.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
              alt="Diverse group of people at an event"
              fill
              priority
              className="object-cover"
            />
          </div>
        </section>

        {/* Our Story Section */}
        <section className="mb-16 md:mb-24 max-w-3xl mx-auto">
          <h2 className="text-3xl font-semibold text-center text-primary mb-6">Our Story</h2>
          <div className="prose prose-lg max-w-none text-muted-foreground">
            <p>
              Fuiyoo started with a simple idea: to make event discovery and participation effortless and enjoyable.
              We noticed that finding local events, from community gatherings to workshops and festivals, often involved
              sifting through various platforms or relying on word-of-mouth. We envisioned a centralized hub where event
              organizers could easily showcase their events and where attendees could find exciting activities tailored
              to their interests.
            </p>
            <p>
              Our journey began with a small team of passionate developers, designers, and event enthusiasts who believed
              in the power of technology to connect people. We spent countless hours brainstorming, coding, and refining
              our platform to create an intuitive and feature-rich experience. We aimed to build more than just a listing
              service; we wanted to foster a community where people could share experiences, discover new passions, and
              make meaningful connections.
            </p>
            <p>
              The name &quot;Fuiyoo&quot; itself reflects our excitement and the joy we hope our users experience. It&apos;s an
              exclamation of delight, a nod to the thrill of discovering something new and wonderful. We wanted a name
              that was memorable, fun, and embodied the spirit of our platform.
            </p>
          </div>
        </section>

        {/* Our Mission Section */}
        <section className="mb-16 md:mb-24 text-center bg-muted py-12 rounded-xl px-6">
          <h2 className="text-3xl font-semibold text-primary mb-4">Our Mission</h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Our mission is to connect communities by making event discovery and management seamless and engaging.
            We strive to empower event organizers with the tools they need to succeed and to provide attendees with
            a diverse range of events that enrich their lives. We believe that events are more than just gatherings;
            they are opportunities for learning, growth, and connection.
          </p>
        </section>

        {/* What We Offer Section */}
        <section className="mb-16 md:mb-24">
          <h2 className="text-3xl font-semibold text-center text-primary mb-8">What We Offer</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              { title: "Comprehensive Event Listings", description: "Discover a wide variety of events, from local meetups and workshops to large-scale conferences and festivals. Our platform is designed to help you find events that match your interests, location, and schedule." },
              { title: "Easy Event Creation", description: "For organizers, Fuiyoo provides intuitive tools to create, manage, and promote events. Showcase your event with detailed descriptions, images, and ticketing options. Reach a wider audience and streamline your event management process." },
              { title: "Seamless Ticketing & Registration", description: "Securely purchase tickets or register for events directly through our platform. We partner with trusted payment gateways to ensure your transactions are safe and smooth. Organizers can manage attendees and track registrations with ease." },
              { title: "Personalized Recommendations", description: "Our smart recommendation engine suggests events based on your preferences and past activity, ensuring you don&apos;t miss out on experiences you&apos;ll love." },
              { title: "Community Engagement", description: "Connect with other attendees, share your experiences, and stay updated on your favorite organizers. Fuiyoo is more than a platform; it&apos;s a community of event-goers and creators." },
              { title: "Organizer Tools", description: "Access analytics, promotional tools, and communication features to make your event a success. Understand your audience, engage with attendees, and grow your reach." },
            ].map(item => (
              <div key={item.title} className="bg-card p-6 rounded-lg shadow-md">
                <h3 className="text-xl font-semibold text-foreground mb-2">{item.title}</h3>
                <p className="text-muted-foreground">{item.description}</p>
              </div>
            ))}
          </div>
        </section>

        {/* Our Values Section */}
        <section className="mb-16 md:mb-24">
          <h2 className="text-3xl font-semibold text-center text-primary mb-8">Our Values</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {[
              { title: "Community-Focused", description: "We believe in the power of events to bring people together and build stronger communities. Our platform is designed to facilitate connections and foster a sense of belonging." },
              { title: "User-Centric Design", description: "We prioritize the user experience in everything we do. Our platform is intuitive, easy to navigate, and designed to meet the needs of both organizers and attendees." },
              { title: "Innovation", description: "We are committed to continuously improving our platform with new features and technologies to enhance the event experience. We embrace creativity and strive to stay ahead of industry trends." },
              { title: "Inclusivity", description: "We aim to create a welcoming environment for everyone. We support a diverse range of events and strive to make our platform accessible to all users." },
              { title: "Trust & Safety", description: "We are committed to providing a secure and reliable platform. We take privacy and data protection seriously and work to ensure a safe environment for all interactions." },
            ].map(value => (
              <div key={value.title} className="bg-muted p-6 rounded-lg text-center">
                <h3 className="text-xl font-semibold text-primary mb-2">{value.title}</h3>
                <p className="text-muted-foreground">{value.description}</p>
              </div>
            ))}
          </div>
        </section>

        {/* Meet the Team Section */}
        <section className="mb-16 md:mb-24">
          <h2 className="text-3xl font-semibold text-center text-primary mb-10">Meet the (Fictional) Team</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {teamMembers.map((member) => (
              <div key={member.id} className="bg-card p-6 rounded-lg shadow-md text-center">
                <Image
                  src={member.imageUrl}
                  alt={member.name}
                  width={120}
                  height={120}
                  className="rounded-full mx-auto mb-4 ring-2 ring-primary"
                />
                <h3 className="text-xl font-semibold text-foreground mb-1">{member.name}</h3>
                <p className="text-primary mb-2">{member.role}</p>
                <p className="text-muted-foreground text-sm">{member.bio}</p>
              </div>
            ))}
          </div>
        </section>

        {/* Join Our Journey Section */}
        <section className="text-center">
          <h2 className="text-3xl font-semibold text-primary mb-4">Join Our Journey</h2>
          <p className="text-lg text-muted-foreground max-w-xl mx-auto mb-8">
            We&apos;re excited about the future of Fuiyoo and the role we can play in connecting people through events.
            Whether you&apos;re an event organizer looking to reach a wider audience or an attendee searching for your
            next great experience, we invite you to join our community. Let&apos;s make every event memorable!
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" className="bg-primary hover:bg-primary/90 text-primary-foreground">
              <Link href="/events">Explore Events</Link>
            </Button>
            <Button asChild size="lg" variant="outline" className="border-primary text-primary hover:bg-primary/5">
              <Link href="/organizer/dashboard">Become an Organizer</Link>
            </Button>
          </div>
        </section>

      </div>
    </main>
  );
}
