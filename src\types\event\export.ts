/**
 * Data export type definitions
 * @module types/event/export
 */

import { ReactNode } from 'react';

/**
 * Export format options
 */
export type ExportFormat = 'json' | 'csv';

/**
 * Export type options
 */
export type ExportType = 'all' | 'profile' | 'activity' | 'consents';

/**
 * Export option configuration
 */
export interface ExportOption {
    /** Unique identifier */
    id: ExportType;
    /** Display title */
    title: string;
    /** Description text */
    description: string;
    /** Icon component */
    icon: ReactNode;
}

/**
 * Export history item
 */
export interface ExportHistoryItem {
    /** Unique identifier */
    id: string;
    /** Export status */
    status: string;
    /** Export format */
    exportFormat: ExportFormat;
    /** Export type */
    exportType: ExportType;
    /** Request timestamp */
    requestedAt: string;
    /** Completion timestamp */
    completedAt?: string;
    /** Expiration timestamp */
    expiresAt?: string;
    /** Number of downloads */
    downloadCount: string;
}

/**
 * Data export component props
 */
export interface DataExportProps {
    /** Optional user ID */
    userId?: string;
} 