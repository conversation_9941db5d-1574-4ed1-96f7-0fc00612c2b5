import { BaseRepository } from './base-repository';
import { createClient } from '@/lib/supabase/pages-client';
import { Database } from '@/types/supabase';
import { PostgrestResponse, PostgrestSingleResponse } from '@supabase/supabase-js';

type EventRow = Database['public']['Tables']['events']['Row'];
type EventInsert = Database['public']['Tables']['events']['Insert'];
type EventUpdate = Database['public']['Tables']['events']['Update'];
type OrganizationRow = Database['public']['Tables']['organizations']['Row'];

// Define the Event type with additional fields
export interface Event extends EventRow {
  organization?: OrganizationRow | null;
  ticket_types?: Array<{
    id: string;
    name: string;
    price: number;
    capacity: number;
    event_id: string;
  }> | null;
}

export class EventRepository extends BaseRepository<'events'> {
  constructor() {
    super('events');
  }

  /**
   * Check if the current user has access to the event
   */
  async checkAccess(id: string, operation: 'read' | 'write'): Promise<boolean> {
    try {
      const supabase = await this.getClient();

      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        return false;
      }

      // Get the event
      const { data: event, error } = await supabase
        .from(this.tableName)
        .select('user_id, organization_id')
        .eq('id', id)
        .single() as PostgrestSingleResponse<EventRow>;

      if (error || !event) {
        return false;
      }

      // Check if the user is the owner
      if (event.user_id === user.id) {
        return true;
      }

      // Check if the user is part of the organization
      if (event.organization_id) {
        const { data: membership, error: membershipError } = await supabase
          .from('organization_members')
          .select('role')
          .eq('organization_id', event.organization_id)
          .eq('user_id', user.id)
          .single();

        if (!membershipError && membership) {
          // For read access, any member can access
          if (operation === 'read') {
            return true;
          }

          // For write access, only admins and owners can access
          if (operation === 'write' &&
            (membership.role === 'admin' || membership.role === 'owner')) {
            return true;
          }
        }
      }

      return false;
    } catch (error) {
      console.error('Error checking event access:', error);
      return false;
    }
  }

  /**
   * Get the current user ID
   */
  async getCurrentUserId(): Promise<string | null> {
    try {
      const supabase = await this.getClient();
      const { data: { user } } = await supabase.auth.getUser();
      return user?.id || null;
    } catch (error) {
      console.error('Error getting current user ID:', error);
      return null;
    }
  }

  /**
   * Get published events (public access)
   */
  async getPublishedEvents(limit = 10, offset = 0): Promise<Event[]> {
    try {
      const supabase = await this.getClient();

      const { data, error } = await supabase
        .from(this.tableName)
        .select(`
          *,
          organization:organization_id(*)
        `)
        .eq('status', 'published')
        .order('start_date', { ascending: true })
        .range(offset, offset + limit - 1) as PostgrestResponse<Event>;

      if (error) {
        console.error('Error fetching published events:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getPublishedEvents:', error);
      return [];
    }
  }

  /**
   * Get event with related details
   */
  async getEventWithDetails(id: string): Promise<Event | null> {
    try {
      // For published events, allow public access
      const supabase = await this.getClient();
      const { data: eventData, error: eventError } = await supabase
        .from(this.tableName)
        .select('*')
        .eq('id', id)
        .single() as PostgrestSingleResponse<EventRow>;

      if (eventError || !eventData) {
        console.error('Error fetching event:', eventError);
        return null;
      }

      // If the event is not published, check authorization
      if (eventData.status !== 'published') {
        const hasAccess = await this.checkAccess(id, 'read');
        if (!hasAccess) {
          throw new Error(`Unauthorized: Cannot read event with ID ${id}`);
        }
      }

      // Fetch related data
      const { data, error } = await supabase
        .from(this.tableName)
        .select(`
          *,
          organization:organization_id(*)
        `)
        .eq('id', id)
        .single() as PostgrestSingleResponse<Event>;

      if (error) {
        console.error('Error fetching event with details:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in getEventWithDetails:', error);
      return null;
    }
  }

  /**
   * Get events for an organization with authorization check
   */
  async getEventsByOrganization(organizationId: string): Promise<Event[]> {
    try {
      const supabase = await this.getClient();

      // Check if the user is a member of the organization
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        return [];
      }

      const { data: membership, error: membershipError } = await supabase
        .from('organization_members')
        .select('role')
        .eq('organization_id', organizationId)
        .eq('user_id', user.id)
        .single();

      if (membershipError || !membership) {
        return [];
      }

      // Fetch events for the organization
      const { data, error } = await supabase
        .from(this.tableName)
        .select(`
          *,
          organization:organization_id(*)
        `)
        .eq('organization_id', organizationId)
        .order('created_at', { ascending: false }) as PostgrestResponse<Event>;

      if (error) {
        console.error('Error fetching organization events:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getEventsByOrganization:', error);
      return [];
    }
  }

  /**
   * Search events
   */
  async searchEvents(query: string, limit = 10): Promise<Event[]> {
    try {
      const supabase = await this.getClient();

      const { data, error } = await supabase
        .from(this.tableName)
        .select(`
          *,
          organization:organization_id(*)
        `)
        .eq('status', 'published')
        .or(`title.ilike.%${query}%, description.ilike.%${query}%`)
        .order('start_date', { ascending: true })
        .limit(limit) as PostgrestResponse<Event>;

      if (error) {
        console.error('Error searching events:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in searchEvents:', error);
      return [];
    }
  }

  /**
   * Get events for the current user
   */
  async getByCurrentUser(): Promise<Event[]> {
    try {
      const supabase = await this.getClient();
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        return [];
      }

      const { data, error } = await supabase
        .from(this.tableName)
        .select(`
          *,
          organization:organization_id(*)
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false }) as PostgrestResponse<Event>;

      if (error) {
        console.error('Error fetching user events:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getByCurrentUser:', error);
      return [];
    }
  }

  /**
   * Publish an event
   */
  async publishEvent(id: string): Promise<Event | null> {
    try {
      const hasAccess = await this.checkAccess(id, 'write');
      if (!hasAccess) {
        throw new Error(`Unauthorized: Cannot publish event with ID ${id}`);
      }

      const supabase = await this.getClient();
      const { data, error } = await supabase
        .from(this.tableName)
        .update({ status: 'published' })
        .eq('id', id)
        .select(`
          *,
          organization:organization_id(*)
        `)
        .single() as PostgrestSingleResponse<Event>;

      if (error) {
        console.error('Error publishing event:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in publishEvent:', error);
      return null;
    }
  }

  /**
   * Cancel an event
   */
  async cancelEvent(id: string): Promise<Event | null> {
    try {
      const hasAccess = await this.checkAccess(id, 'write');
      if (!hasAccess) {
        throw new Error(`Unauthorized: Cannot cancel event with ID ${id}`);
      }

      const supabase = await this.getClient();
      const { data, error } = await supabase
        .from(this.tableName)
        .update({ status: 'cancelled' })
        .eq('id', id)
        .select(`
          *,
          organization:organization_id(*)
        `)
        .single() as PostgrestSingleResponse<Event>;

      if (error) {
        console.error('Error cancelling event:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in cancelEvent:', error);
      return null;
    }
  }
}
