/**
 * Database schema type definitions
 * @module types/database/schema
 */

/**
 * Organization table schema
 */
export interface Organization {
    /** Unique identifier */
    id: string;
    /** Organization name */
    name: string;
    /** Organization slug */
    slug: string;
    /** Organization description */
    description?: string;
    /** Organization logo URL */
    logoUrl?: string;
    /** Organization website */
    website?: string;
    /** Organization email */
    email?: string;
    /** Organization phone */
    phone?: string;
    /** Organization address */
    address?: string;
    /** Organization city */
    city?: string;
    /** Organization state/province */
    state?: string;
    /** Organization country */
    country?: string;
    /** Organization postal code */
    postalCode?: string;
    /** Creation timestamp */
    createdAt: Date;
    /** Last update timestamp */
    updatedAt: Date;
}

/**
 * Event table schema
 */
export interface EventRecord {
    /** Unique identifier */
    id: string;
    /** Organization ID */
    organizationId: string;
    /** Event name */
    name: string;
    /** Event slug */
    slug: string;
    /** Event description */
    description?: string;
    /** Event start date */
    startDate: Date;
    /** Event end date */
    endDate: Date;
    /** Event timezone */
    timezone: string;
    /** Event location */
    location?: string;
    /** Event type */
    type: string;
    /** Event status */
    status: string;
    /** Event banner image URL */
    bannerUrl?: string;
    /** Event website */
    website?: string;
    /** Event registration settings */
    registrationSettings: Record<string, unknown>;
    /** Creation timestamp */
    createdAt: Date;
    /** Last update timestamp */
    updatedAt: Date;
}

/**
 * User table schema
 */
export interface User {
    /** Unique identifier */
    id: string;
    /** User email */
    email: string;
    /** User display name */
    displayName: string;
    /** User first name */
    firstName?: string;
    /** User last name */
    lastName?: string;
    /** User avatar URL */
    avatarUrl?: string;
    /** User phone number */
    phone?: string;
    /** User address */
    address?: string;
    /** User city */
    city?: string;
    /** User state/province */
    state?: string;
    /** User country */
    country?: string;
    /** User postal code */
    postalCode?: string;
    /** User metadata */
    metadata: Record<string, unknown>;
    /** Creation timestamp */
    createdAt: Date;
    /** Last update timestamp */
    updatedAt: Date;
}

/**
 * Registration table schema
 */
export interface Registration {
    /** Unique identifier */
    id: string;
    /** Event ID */
    eventId: string;
    /** Category ID */
    categoryId: string;
    /** User ID */
    userId: string;
    /** Registration status */
    status: string;
    /** Registration number */
    registrationNumber: string;
    /** Registration data */
    data: Record<string, unknown>;
    /** Payment status */
    paymentStatus: string;
    /** Payment amount */
    paymentAmount: number;
    /** Payment currency */
    paymentCurrency: string;
    /** Payment reference */
    paymentReference?: string;
    /** Creation timestamp */
    createdAt: Date;
    /** Last update timestamp */
    updatedAt: Date;
} 