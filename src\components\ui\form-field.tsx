import * as React from "react";
import { Input } from "./input";
import { Textarea } from "./textarea";
import { cn } from "@/lib/utils";

type FormFieldProps = {
    error?: string | undefined;
    multiline?: boolean;
} & (
        | (Omit<React.ComponentProps<"input">, "ref"> & { multiline?: false })
        | (Omit<React.ComponentProps<"textarea">, "ref"> & { multiline: true })
    );

const FormField = React.forwardRef<HTMLInputElement | HTMLTextAreaElement, FormFieldProps>(
    ({ className, error, multiline, ...props }, ref) => {
        if (multiline) {
            return (
                <div className="space-y-1">
                    <Textarea
                        className={cn(
                            error && "border-destructive",
                            className
                        )}
                        {...(props as React.ComponentProps<"textarea">)}
                        ref={ref as React.Ref<HTMLTextAreaElement>}
                    />
                    {error && (
                        <p className="text-sm text-destructive">{error}</p>
                    )}
                </div>
            );
        }

        return (
            <div className="space-y-1">
                <Input
                    className={cn(
                        error && "border-destructive",
                        className
                    )}
                    {...(props as React.ComponentProps<"input">)}
                    ref={ref as React.Ref<HTMLInputElement>}
                />
                {error && (
                    <p className="text-sm text-destructive">{error}</p>
                )}
            </div>
        );
    }
);

FormField.displayName = "FormField";

export { FormField }; 