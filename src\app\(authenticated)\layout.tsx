export default function AuthenticatedLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="min-h-screen bg-background">
      <div className="flex min-h-screen">
        {/* Sidebar */}
        <div className="w-64 border-r">
          {/* ... sidebar content ... */}
        </div>

        {/* Main content */}
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <header className="h-16 border-b">
            {/* ... header content ... */}
          </header>

          {/* Main content area */}
          <main className="flex-1 p-6 overflow-y-auto main-content">
            {children}
          </main>

          {/* Footer */}
          <footer className="h-16 border-t">
            {/* ... footer content ... */}
          </footer>
        </div>
      </div>
    </div>
  )
}