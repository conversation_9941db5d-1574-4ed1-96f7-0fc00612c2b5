'use server';

import { createServerActionClient } from "@/lib/supabase/actions";
import { revalidatePath } from "next/cache";
import { logger } from '@/lib/logger';
import {
  type Contact,
  type ContactDatabase,
  type ContactResponse,
  ContactDatabaseSchema,
  transformDatabaseContact,
  transformContactToDatabase
} from '@/types/contacts/schema';

/**
 * Get all saved contacts for the current user
 */
export async function getSavedContacts(): Promise<ContactResponse<Contact[]>> {
  try {
    const supabase = await createServerActionClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.user?.id) {
      return {
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      };
    }

    const { data: contacts, error } = await supabase
      .from('saved_contacts')
      .select('*')
      .eq('user_id', session.user.id);

    if (error) {
      logger.error('Error fetching contacts:', error);
      return {
        error: {
          code: 'DATABASE_ERROR',
          message: 'Failed to fetch contacts',
          details: error
        }
      };
    }

    // Validate and transform the data
    const validatedContacts = (contacts || []).map(contact => {
      const result = ContactDatabaseSchema.safeParse(contact);
      if (!result.success) {
        logger.error('Contact validation failed:', result.error);
        return null;
      }
      return transformDatabaseContact(result.data);
    }).filter((contact): contact is Contact => contact !== null);

    return { data: validatedContacts };
  } catch (error) {
    logger.error('Unexpected error in getSavedContacts:', error);
    return {
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An unexpected error occurred',
        details: error
      }
    };
  }
}

/**
 * Get a single contact by ID
 */
export async function getContactById(contactId: string): Promise<ContactResponse<Contact>> {
  try {
    const supabase = await createServerActionClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.user?.id) {
      return {
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      };
    }

    const { data, error } = await supabase
      .from('saved_contacts')
      .select('*')
      .eq('id', contactId)
      .eq('user_id', session.user.id)
      .single();

    if (error) {
      logger.error(`Error fetching contact:`, error);
      return {
        error: {
          code: 'DATABASE_ERROR',
          message: 'Failed to fetch contact',
          details: error
        }
      };
    }

    const result = ContactDatabaseSchema.safeParse(data);
    if (!result.success) {
      logger.error('Contact validation failed:', result.error);
      return {
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid contact data',
          details: result.error
        }
      };
    }

    return { data: transformDatabaseContact(result.data) };
  } catch (error) {
    logger.error("Error in getContactById:", error);
    return {
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An unexpected error occurred',
        details: error
      }
    };
  }
}

/**
 * Create a new contact
 */
export async function createContact(contact: ContactDatabase): Promise<ContactResponse<Contact>> {
  try {
    const supabase = await createServerActionClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.user?.id) {
      return {
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      };
    }

    // Remove fields that should not be included in insert
    const { id, created_at, updated_at, ...insertData } = contact;

    const { data, error } = await supabase
      .from('saved_contacts')
      .insert({ ...insertData, user_id: session.user.id })
      .select()
      .single();

    if (error) {
      logger.error('Error creating contact:', error);
      return {
        error: {
          code: 'DATABASE_ERROR',
          message: 'Failed to create contact',
          details: error
        }
      };
    }

    const result = ContactDatabaseSchema.safeParse(data);
    if (!result.success) {
      logger.error('Contact validation failed:', result.error);
      return {
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid contact data',
          details: result.error
        }
      };
    }

    revalidatePath('/contacts');
    return { data: transformDatabaseContact(result.data) };
  } catch (error) {
    logger.error('Unexpected error in createContact:', error);
    return {
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An unexpected error occurred',
        details: error
      }
    };
  }
}

/**
 * Update an existing contact
 */
export async function updateContact(id: string, contact: ContactDatabase): Promise<ContactResponse<Contact>> {
  try {
    const supabase = await createServerActionClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.user?.id) {
      return {
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      };
    }

    // Remove fields that should not be included in update
    const { id: contactId, user_id, created_at, updated_at, ...updateData } = contact;

    const { data, error } = await supabase
      .from('saved_contacts')
      .update(updateData)
      .eq('id', id)
      .eq('user_id', session.user.id)
      .select()
      .single();

    if (error) {
      logger.error('Error updating contact:', error);
      return {
        error: {
          code: 'DATABASE_ERROR',
          message: 'Failed to update contact',
          details: error
        }
      };
    }

    const result = ContactDatabaseSchema.safeParse(data);
    if (!result.success) {
      logger.error('Contact validation failed:', result.error);
      return {
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid contact data',
          details: result.error
        }
      };
    }

    revalidatePath('/contacts');
    return { data: transformDatabaseContact(result.data) };
  } catch (error) {
    logger.error('Unexpected error in updateContact:', error);
    return {
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An unexpected error occurred',
        details: error
      }
    };
  }
}

/**
 * Delete a contact
 */
export async function deleteContact(id: string): Promise<ContactResponse<void>> {
  try {
    const supabase = await createServerActionClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.user?.id) {
      return {
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      };
    }

    const { error } = await supabase
      .from('saved_contacts')
      .delete()
      .eq('id', id)
      .eq('user_id', session.user.id);

    if (error) {
      logger.error('Error deleting contact:', error);
      return {
        error: {
          code: 'DATABASE_ERROR',
          message: 'Failed to delete contact',
          details: error
        }
      };
    }

    revalidatePath('/contacts');
    return { data: undefined };
  } catch (error) {
    logger.error('Unexpected error in deleteContact:', error);
    return {
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An unexpected error occurred',
        details: error
      }
    };
  }
}