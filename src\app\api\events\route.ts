import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { EventRepository } from '@/repositories/event-repository';
import { EventRecord } from '@/types/database';
import { Database } from '@/lib/supabase/types';
import { SupabaseClient } from '@supabase/supabase-js';

type EventImage = Database['public']['Tables']['event_images']['Row'];
type EventRegistration = Database['public']['Tables']['registrations']['Row'];
type Review = Database['public']['Tables']['reviews']['Row'];
type Event = Database['public']['Tables']['events']['Row'];

interface FormattedEvent {
  id: string
  title: string
  description: string
  startDate: string
  endDate: string
  location: string
  status: string | null
  poster: { url: string } | null
  gallery: { url: string }[]
  createdAt: string
  updatedAt: string
}

export async function GET() {
  const supabase = await createClient();

  try {
    const { data: events, error } = await supabase
      .from('events')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    return NextResponse.json({ events });
  } catch (error) {
    console.error('Error fetching events:', error);
    return NextResponse.json(
      { error: 'Failed to fetch events' },
      { status: 500 }
    );
  }
}

async function getEventById(eventId: string) {
  try {
    const eventRepository = new EventRepository();
    const event = await eventRepository.getEventById(eventId);

    if (!event) {
      return new NextResponse("Event not found", { status: 404 });
    }

    // Get the event images (still using Supabase for now)
    const supabase = await createClient();
    const { data: images, error: imagesError } = await supabase
      .from('event_images')
      .select('*')
      .eq('event_id', eventId)
      .order('created_at', { ascending: false });

    if (imagesError) {
      console.error("Error fetching event images:", imagesError);
      // Continue without images
    }

    // Process images if available
    const poster = images?.find(img => img.type === 'poster');
    const gallery = images?.filter(img => img.type === 'gallery') || [];

    return NextResponse.json({
      ...event,
      poster: poster ? { url: poster.url } : null,
      gallery: gallery.map(img => ({ url: img.url }))
    });
  } catch (error) {
    console.error("Error fetching event:", error);
    return new NextResponse("Error fetching event", { status: 500 });
  }
}

async function getUpcomingEvents(userId: string) {
  try {
    const supabase = await createClient();
    const eventRepository = new EventRepository();
    const currentDate = new Date().toISOString();

    // First get the user's registrations
    const { data: registrations, error } = await supabase
      .from('registrations')
      .select('event_id, ticket_number, attendance_status')
      .eq('user_id', userId);

    if (error) {
      console.error("Error fetching registrations:", error);
      return new NextResponse("Error fetching events", { status: 500 });
    }

    if (!registrations || registrations.length === 0) {
      return NextResponse.json([]);
    }

    // Get all the event IDs from registrations
    const eventIds = registrations.map(reg => reg.event_id);

    // Get all events
    const allEvents = await eventRepository.getAllEvents();

    // Filter for the user's registered upcoming events
    const userEvents = allEvents.filter(event =>
      eventIds.includes(event.id) &&
      event.endDate && new Date(event.endDate) >= new Date(currentDate)
    );

    // Sort by start date (ascending)
    userEvents.sort((a, b) => {
      // Handle null or undefined dates safely
      const aDate = a.startDate ? new Date(a.startDate).getTime() : 0;
      const bDate = b.startDate ? new Date(b.startDate).getTime() : 0;
      return aDate - bDate;
    });

    // Create a map of registration details by event ID
    const registrationDetailsByEventId = registrations.reduce<Record<string, { ticket_number: string | null, status: string | null }>>((acc, reg) => {
      acc[reg.event_id] = {
        ticket_number: reg.ticket_number || null,
        status: reg.attendance_status || null
      };
      return acc;
    }, {});

    // Add registration details to events
    const eventsWithDetails = userEvents.map(event => ({
      ...event,
      registration: registrationDetailsByEventId[event.id] || { ticket_number: null, status: null }
    }));

    return NextResponse.json(eventsWithDetails);
  } catch (error) {
    console.error("Error fetching upcoming events:", error);
    return new NextResponse("Error fetching events", { status: 500 });
  }
}

async function getPastEvents(userId: string) {
  try {
    const supabase = await createClient();
    const eventRepository = new EventRepository();
    const currentDate = new Date().toISOString();

    // First get the user's registrations
    const { data: registrations, error } = await supabase
      .from('registrations')
      .select('event_id')
      .eq('user_id', userId);

    if (error) {
      console.error("Error fetching registrations:", error);
      return new NextResponse("Error fetching events", { status: 500 });
    }

    if (!registrations || registrations.length === 0) {
      return NextResponse.json([]);
    }

    // Get all the event IDs from registrations
    const eventIds = registrations.map(reg => reg.event_id);

    // Get all events
    const allEvents = await eventRepository.getAllEvents();

    // Filter for the user's registered past events
    const userEvents = allEvents.filter(event =>
      eventIds.includes(event.id) &&
      event.endDate && new Date(event.endDate) < new Date(currentDate)
    );

    // Sort by start date (descending for past events)
    userEvents.sort((a, b) => {
      // Handle null or undefined dates safely
      const aDate = a.startDate ? new Date(a.startDate).getTime() : 0;
      const bDate = b.startDate ? new Date(b.startDate).getTime() : 0;
      return bDate - aDate;
    });

    // Get reviews for these events
    const { data: reviews, error: reviewsError } = await supabase
      .from('reviews')
      .select('event_id, rating, comment')
      .in('event_id', userEvents.map(e => e.id))
      .eq('user_id', userId);

    if (reviewsError) {
      console.error("Error fetching reviews:", reviewsError);
      // Continue without reviews
    }

    // Create a map of review details by event ID
    const reviewDetailsByEventId = (reviews || []).reduce<Record<string, { rating: number | null, feedback: string | null }>>((acc, review) => {
      acc[review.event_id] = {
        rating: review.rating || null,
        feedback: review.comment || null
      };
      return acc;
    }, {});

    // Add review details to events
    const eventsWithDetails = userEvents.map(event => ({
      ...event,
      registration: reviewDetailsByEventId[event.id] || { rating: null, feedback: null }
    }));

    return NextResponse.json(eventsWithDetails);
  } catch (error) {
    console.error("Error fetching past events:", error);
    return new NextResponse("Error fetching events", { status: 500 });
  }
}

async function getRecommendedEvents(userId: string) {
  try {
    const supabase = await createClient();
    const currentDate = new Date().toISOString();

    // Get user's past registrations to analyze preferences
    const { data: pastRegistrations, error: regError } = await supabase
      .from('registrations')
      .select('event_id')
      .eq('user_id', userId);

    if (regError) {
      console.error("Error fetching past registrations:", regError);
      return new NextResponse("Error fetching events", { status: 500 });
    }

    // Get all upcoming events
    const { data: upcomingEvents, error: eventsError } = await supabase
      .from('events')
      .select('*')
      .gt('end_date', currentDate)
      .order('start_date', { ascending: true });

    if (eventsError) {
      console.error("Error fetching upcoming events:", eventsError);
      return new NextResponse("Error fetching events", { status: 500 });
    }

    // Filter out events user is already registered for
    const registeredEventIds = new Set((pastRegistrations || []).map(reg => reg.event_id));
    const recommendedEvents = (upcomingEvents || []).filter(event => !registeredEventIds.has(event.id));

    return NextResponse.json(recommendedEvents);
  } catch (error) {
    console.error("Error fetching recommended events:", error);
    return new NextResponse("Error fetching events", { status: 500 });
  }
}

async function getAllEvents() {
  try {
    const eventRepository = new EventRepository();
    const events = await eventRepository.getAllEvents();

    // Fetch poster images for each event
    if (events.length > 0) {
      const supabase = await createClient();
      const eventIds = events.map(event => event.id);

      const { data: eventImages } = await supabase
        .from('event_images')
        .select('*')
        .in('event_id', eventIds)
        .eq('type', 'poster');

      // Add poster URLs to events
      if (eventImages) {
        const posterMap = eventImages.reduce<Record<string, string>>((acc, img: EventImage) => {
          acc[img.event_id] = img.url;
          return acc;
        }, {});

        const eventsWithPosters = events.map(event => ({
          ...event,
          poster: posterMap[event.id] ? { url: posterMap[event.id] } : null
        }));

        return NextResponse.json(eventsWithPosters);
      }
    }

    return NextResponse.json(events);
  } catch (error) {
    console.error("Error fetching all events:", error);
    return new NextResponse("Error fetching events", { status: 500 });
  }
}

interface CreateEventData {
  title: string
  description: string
  startDate: string
  endDate: string
  location: string
  status?: string
  organizerId: string
}

export async function POST(request: Request) {
  const supabase = await createClient();

  try {
    const data: CreateEventData = await request.json();

    const { data: event, error } = await supabase
      .from('events')
      .insert({
        title: data.title,
        description: data.description,
        start_date: data.startDate,
        end_date: data.endDate,
        location: data.location,
        status: data.status || null,
        organizer_id: data.organizerId
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return NextResponse.json({ event });
  } catch (error) {
    console.error('Error creating event:', error);
    return NextResponse.json(
      { error: 'Failed to create event' },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request) {
  try {
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session || !session.user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const userId = session.user.id;

    const body = await request.json();

    if (!body.id) {
      return new NextResponse("Missing event ID", { status: 400 });
    }

    try {
      const eventRepository = new EventRepository();

      // Verify the user owns this event
      const existingEvent = await eventRepository.getEventById(body.id);

      if (!existingEvent) {
        return new NextResponse("Event not found", { status: 404 });
      }

      if (existingEvent.organizerId !== userId && existingEvent.createdBy !== userId) {
        return new NextResponse("Unauthorized - You don't have permission to update this event", { status: 403 });
      }

      // Update the event
      const updatedEvent = await eventRepository.updateEvent(body.id, {
        title: body.title,
        description: body.description,
        eventTypeId: body.eventTypeId,
        location: body.venue || body.location,
        state: body.state,
        country: body.country,
        startDate: body.startDate,
        endDate: body.endDate,
        timezone: body.timezone,
        status: body.status,
        emergencyContactSettings: body.emergencyContactSettings
      });

      // Handle poster image if provided
      if (body.poster) {
        const supabase = await createClient();

        // Check if poster already exists
        const { data: existingPosters } = await supabase
          .from('event_images')
          .select('*')
          .eq('event_id', body.id)
          .eq('type', 'poster');

        if (existingPosters && existingPosters.length > 0) {
          // Update existing poster
          // Check if existingPosters has elements before accessing index 0
          if (existingPosters && existingPosters.length > 0 && existingPosters[0]?.id) {
            const { error: updateError } = await supabase
              .from('event_images')
              .update({
                url: body.poster.url,
                path: body.poster.path,
                updated_at: new Date().toISOString()
              })
              .eq('id', existingPosters[0].id);

            if (updateError) {
              console.error("Error updating poster image:", updateError);
            }
          } else {
            console.error("No existing poster found with valid ID");
          }


        } else {
          // Insert new poster
          const { error: insertError } = await supabase
            .from('event_images')
            .insert({
              event_id: body.id,
              type: 'poster',
              url: body.poster.url,
              path: body.poster.path,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });

          if (insertError) {
            console.error("Error adding poster image:", insertError);
          }
        }
      }

      return NextResponse.json({
        success: true,
        data: { event: updatedEvent }
      });
    } catch (error) {
      console.error("Error updating event:", error);
      return new NextResponse(`Error updating event: ${error instanceof Error ? error.message : 'Unknown error'}`, { status: 500 });
    }
  } catch (error) {
    console.error("Error in PUT handler:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

export async function DELETE(request: Request) {
  try {
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session || !session.user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const userId = session.user.id;

    const { searchParams } = new URL(request.url);
    const eventId = searchParams.get('id');

    if (!eventId) {
      return new NextResponse("Missing event ID", { status: 400 });
    }

    try {
      const eventRepository = new EventRepository();

      // Verify the user owns this event
      const existingEvent = await eventRepository.getEventById(eventId);

      if (!existingEvent) {
        return new NextResponse("Event not found", { status: 404 });
      }

      if (existingEvent.organizerId !== userId && existingEvent.createdBy !== userId) {
        return new NextResponse("Unauthorized - You don't have permission to delete this event", { status: 403 });
      }

      // Delete the event
      await eventRepository.deleteEvent(eventId);

      // Perform any additional cleanup (e.g., removing event images)
      const supabase = await createClient();

      const { error: imageError } = await supabase
        .from('event_images')
        .delete()
        .eq('event_id', eventId);

      if (imageError) {
        console.error("Error deleting event images:", imageError);
        // Continue even if image deletion failed
      }

      return NextResponse.json({
        success: true,
        message: "Event deleted successfully"
      });
    } catch (error) {
      console.error("Error deleting event:", error);
      return new NextResponse(`Error deleting event: ${error instanceof Error ? error.message : 'Unknown error'}`, { status: 500 });
    }
  } catch (error) {
    console.error("Error in DELETE handler:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}