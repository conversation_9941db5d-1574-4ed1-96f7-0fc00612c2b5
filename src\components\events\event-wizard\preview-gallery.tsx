'use client';

import { useState } from 'react';
import Image from 'next/image';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { ImageObject } from '@/types/event-types';

interface PreviewGalleryProps {
  posterImage: ImageObject | null | undefined;
  galleryImages?: ImageObject[] | null;
  className?: string;
}

export function PreviewGallery({
  posterImage,
  galleryImages,
  className = '',
}: PreviewGalleryProps) {
  const fallbackImage = '/images/fallback/fallback-default.svg';
  const [imageError, setImageError] = useState(false);

  if (!posterImage && (!galleryImages || galleryImages.length === 0)) {
    return (
      <div className={`bg-[hsl(var(--muted))] rounded-lg flex items-center justify-center h-64 ${className}`}>
        <p className="text-[hsl(var(--muted-foreground))]">No images available</p>
      </div>
    );
  }

  const handleImageError = () => {
    setImageError(true);
  };

  const imageSrc = imageError ? fallbackImage : (posterImage?.url || '');

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Main image display - full width */}
      {posterImage && (
        <div className="relative overflow-hidden rounded-lg bg-[hsl(var(--muted))]">
          <div className="relative w-full aspect-[9/16]">
            <Image
              src={imageSrc}
              alt="Event poster"
              fill
              className="object-contain"
              onError={handleImageError}
              sizes="100vw"
              priority />
          </div>
        </div>
      )}

      {/* Gallery images grid */}
      {galleryImages && galleryImages.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {galleryImages.map((image, index) => (
            <div key={index} className="relative aspect-square rounded-lg overflow-hidden bg-[hsl(var(--muted))]">
              <Image
                src={image.url}
                alt={`Gallery image ${index + 1}`}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 50vw, 33vw" />
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
