import { z } from 'zod';

export enum FieldType {
    TEXT = 'text',
    TEXTAREA = 'textarea',
    NUMBER = 'number',
    EMAIL = 'email',
    PHONE = 'phone',
    DATE = 'date',
    TIME = 'time',
    DATETIME = 'datetime',
    SELECT = 'select',
    MULTISELECT = 'multiselect',
    CHECKBOX = 'checkbox',
    RADIO = 'radio',
    FILE = 'file',
    IMAGE = 'image',
    HEADING = 'heading',
    PARAGRAPH = 'paragraph',
    DIVIDER = 'divider',
    CUSTOM = 'custom'
}

// Base field interface
export interface BaseField {
    id: string;
    type: FieldType;
    label: string;
    description?: string | null;
    order?: number;
}

// Interface for fields that accept input
export interface InputField extends BaseField {
    required?: boolean;
    placeholder?: string | null;
    defaultValue?: unknown | null;
}

// Text-based field interface
export interface TextField extends InputField {
    type: FieldType.TEXT | FieldType.TEXTAREA | FieldType.EMAIL | FieldType.PHONE;
    minLength?: number | null;
    maxLength?: number | null;
}

// Number field interface
export interface NumberField extends InputField {
    type: FieldType.NUMBER;
    min?: number | null;
    max?: number | null;
    step?: number | null;
}

// Option type for select fields
export type SelectOption = string | { label: string; value: string };

// Select field interface
export interface SelectField extends InputField {
    type: FieldType.SELECT | FieldType.MULTISELECT | FieldType.CHECKBOX | FieldType.RADIO;
    options: SelectOption[];
}

// Display field interface
export interface DisplayField extends BaseField {
    type: FieldType.HEADING | FieldType.PARAGRAPH | FieldType.DIVIDER;
    content?: string | null;
}

// Date field interface
export interface DateField extends InputField {
    type: FieldType.DATE | FieldType.TIME | FieldType.DATETIME;
    min?: string | null;
    max?: string | null;
}

// File field interface
export interface FileField extends InputField {
    type: FieldType.FILE | FieldType.IMAGE;
    accept?: string | null;
    maxSize?: number | null;
}

// Custom field interface
export interface CustomField extends InputField {
    type: FieldType.CUSTOM;
    component: string;
    props: Record<string, unknown>;
}

// Union type for all field types
export type EventField =
    | TextField
    | NumberField
    | SelectField
    | DisplayField
    | DateField
    | FileField
    | CustomField;

export interface EventType {
    id: string;
    name: string;
    slug: string;
    description?: string;
    icon?: string;
    fields?: EventField[];
    defaultCategories?: Array<{
        name: string;
        description?: string;
        properties?: {
            price?: number;
            capacity?: number;
            registrationLimit?: number;
            startTime?: string;
            earlyBirdPrice?: number;
        };
    }>;
    customFields?: EventField[];
    requireEmergencyContact?: boolean;
    requireTshirtSize?: boolean;
    allowCategorySpecificClosingDates?: boolean;
    status?: 'active' | 'inactive';
    createdAt?: string;
    updatedAt?: string;
    baseFields?: Record<string, any>;
}

export interface EventCategory {
    id?: string;
    name: string;
    description?: string;
    price?: number;
    startTime?: string;
    earlyBirdPrice?: number;
    earlyBirdEndDate?: string;
    bibPrefix?: string;
    bibStartNumber?: string | number;
    bibRequireGeneration?: boolean;
    registrationOpen?: boolean;
    registrationCloseDate?: string;
    registrationLimit?: number;
    customFields?: EventField[];
    properties?: Record<string, any>;
    eventId?: string;
}

export interface EmergencyContactSettings {
    required: boolean;
    fields: string[];
    allowSameForMultipleRegistrations: boolean;
}

export interface TshirtOptions {
    enabled: boolean | null;
    sizes: string[] | null;
    description: string | null;
    sizeChartImage?: {
        url: string;
        path?: string;
    } | null;
}

export interface ImageObject {
    url: string;
    path?: string;
}

// Re-export event status type
export type EventStatus = 'draft' | 'published' | 'cancelled' | 'completed';

// Zod schemas
export const eventFieldSchema = z.discriminatedUnion('type', [
    z.object({
        id: z.string(),
        type: z.enum([FieldType.TEXT, FieldType.TEXTAREA, FieldType.EMAIL, FieldType.PHONE]),
        label: z.string(),
        description: z.string().optional(),
        required: z.boolean().optional(),
        placeholder: z.string().optional(),
        minLength: z.number().optional(),
        maxLength: z.number().optional(),
        defaultValue: z.any().optional(),
        order: z.number().optional(),
        options: z.array(z.union([z.string(), z.object({ label: z.string(), value: z.string() })])).optional(),
    }),
    z.object({
        id: z.string(),
        type: z.literal(FieldType.NUMBER),
        label: z.string(),
        description: z.string().optional(),
        required: z.boolean().optional(),
        placeholder: z.string().optional(),
        min: z.number().optional(),
        max: z.number().optional(),
        step: z.number().optional(),
        defaultValue: z.any().optional(),
        order: z.number().optional(),
        options: z.array(z.union([z.string(), z.object({ label: z.string(), value: z.string() })])).optional(),
    }),
    z.object({
        id: z.string(),
        type: z.enum([FieldType.SELECT, FieldType.MULTISELECT, FieldType.RADIO, FieldType.CHECKBOX]),
        label: z.string(),
        description: z.string().optional(),
        required: z.boolean().optional(),
        placeholder: z.string().optional(),
        options: z.array(z.union([z.string(), z.object({ label: z.string(), value: z.string() })])),
        defaultValue: z.any().optional(),
        order: z.number().optional(),
    }),
    z.object({
        id: z.string(),
        type: z.enum([FieldType.HEADING, FieldType.PARAGRAPH, FieldType.DIVIDER]),
        label: z.string(),
        description: z.string().optional(),
        content: z.string(),
        order: z.number().optional(),
        options: z.array(z.union([z.string(), z.object({ label: z.string(), value: z.string() })])).optional(),
    }),
    z.object({
        id: z.string(),
        type: z.enum([FieldType.DATE, FieldType.TIME, FieldType.DATETIME]),
        label: z.string(),
        description: z.string().optional(),
        required: z.boolean().optional(),
        placeholder: z.string().optional(),
        min: z.string().optional(),
        max: z.string().optional(),
        defaultValue: z.any().optional(),
        order: z.number().optional(),
        options: z.array(z.union([z.string(), z.object({ label: z.string(), value: z.string() })])).optional(),
    }),
    z.object({
        id: z.string(),
        type: z.enum([FieldType.FILE, FieldType.IMAGE]),
        label: z.string(),
        description: z.string().optional(),
        required: z.boolean().optional(),
        accept: z.string().optional(),
        maxSize: z.number().optional(),
        defaultValue: z.any().optional(),
        order: z.number().optional(),
        options: z.array(z.union([z.string(), z.object({ label: z.string(), value: z.string() })])).optional(),
    }),
    z.object({
        id: z.string(),
        type: z.literal(FieldType.CUSTOM),
        label: z.string(),
        description: z.string().optional(),
        required: z.boolean().optional(),
        component: z.string(),
        props: z.record(z.any()),
        defaultValue: z.any().optional(),
        order: z.number().optional(),
        options: z.array(z.union([z.string(), z.object({ label: z.string(), value: z.string() })])).optional(),
    }),
]);

export const eventTypeSchema = z.object({
    id: z.string(),
    name: z.string(),
    slug: z.string(),
    description: z.string().optional(),
    icon: z.string().optional(),
    fields: z.array(eventFieldSchema).optional(),
    defaultCategories: z.array(z.object({
        name: z.string(),
        description: z.string().optional(),
        properties: z.object({
            price: z.number().optional(),
            capacity: z.number().optional(),
            registrationLimit: z.number().optional(),
            startTime: z.string().optional(),
            earlyBirdPrice: z.number().optional(),
        }).optional(),
    })).optional(),
    customFields: z.array(eventFieldSchema).optional(),
    requireEmergencyContact: z.boolean().optional(),
    requireTshirtSize: z.boolean().optional(),
    allowCategorySpecificClosingDates: z.boolean().optional(),
    status: z.enum(['active', 'inactive']).optional(),
    createdAt: z.string().optional(),
    updatedAt: z.string().optional(),
    baseFields: z.record(z.any()).optional(),
});

export const eventCategorySchema = z.object({
    id: z.string().optional(),
    name: z.string(),
    description: z.string().optional(),
    price: z.number().optional(),
    startTime: z.string().optional(),
    earlyBirdPrice: z.number().optional(),
    earlyBirdEndDate: z.string().optional(),
    bibPrefix: z.string().optional(),
    bibStartNumber: z.union([z.string(), z.number()]).optional(),
    bibRequireGeneration: z.boolean().optional(),
    registrationOpen: z.boolean().optional(),
    registrationCloseDate: z.string().optional(),
    registrationLimit: z.number().optional(),
    customFields: z.array(eventFieldSchema).optional(),
    properties: z.record(z.any()).optional(),
    eventId: z.string().optional(),
});

export type BaseEventCategory = Omit<EventCategory, 'properties'>;
export type RunningEventCategory = EventCategory & { properties: { distance?: number; terrain?: string } };
export type ConferenceEventCategory = EventCategory & { properties: { sessions?: string[]; tracks?: string[] } };

export const baseEventCategorySchema = eventCategorySchema.omit({ properties: true });
export const runningEventCategoryPropertiesSchema = z.object({
    distance: z.number().optional(),
    terrain: z.string().optional(),
});
export const conferenceEventCategoryPropertiesSchema = z.object({
    sessions: z.array(z.string()).optional(),
    tracks: z.array(z.string()).optional(),
});

export interface FieldMapping {
    id?: string;
    sourceField: string;
    targetField: string;
    eventTypeId: string;
    createdAt?: string;
    updatedAt?: string;
}

export const fieldMappingSchema = z.object({
    id: z.string().optional(),
    sourceField: z.string(),
    targetField: z.string(),
    eventTypeId: z.string(),
    createdAt: z.string().optional(),
    updatedAt: z.string().optional(),
});

export interface RegistrationField {
    id?: string;
    name: string;
    type: FieldType;
    required: boolean;
    eventTypeId: string;
    createdAt?: string;
    updatedAt?: string;
}

export const registrationFieldSchema = z.object({
    id: z.string().optional(),
    name: z.string(),
    type: z.nativeEnum(FieldType),
    required: z.boolean(),
    eventTypeId: z.string(),
    createdAt: z.string().optional(),
    updatedAt: z.string().optional(),
});

// Type guards for better type narrowing
export const isDisplayField = (field: EventField): field is DisplayField => {
    return [FieldType.HEADING, FieldType.PARAGRAPH, FieldType.DIVIDER].includes(field.type);
};

export const isTextField = (field: EventField): field is TextField => {
    return [FieldType.TEXT, FieldType.TEXTAREA, FieldType.EMAIL, FieldType.PHONE].includes(field.type);
};

export const isNumberField = (field: EventField): field is NumberField => {
    return field.type === FieldType.NUMBER;
};

export const isSelectField = (field: EventField): field is SelectField => {
    return [FieldType.SELECT, FieldType.MULTISELECT, FieldType.CHECKBOX, FieldType.RADIO].includes(field.type);
};

export const isDateField = (field: EventField): field is DateField => {
    return [FieldType.DATE, FieldType.TIME, FieldType.DATETIME].includes(field.type);
};

export const isFileField = (field: EventField): field is FileField => {
    return [FieldType.FILE, FieldType.IMAGE].includes(field.type);
};

export const isCustomField = (field: EventField): field is CustomField => {
    return field.type === FieldType.CUSTOM;
}; 