'use server';

import { createServerActionClient } from "@/lib/supabase/actions";
import { revalidatePath } from "next/cache";
import { ConsentSchema, ConsentVersionSchema, type ConsentFormData } from "@/lib/schemas/privacy";
import { asUnknown, type SqlExecResult, type ExtendedSupabaseClient } from "@/lib/supabase/extended-types";
import { createClient } from '@/lib/supabase/pages-client';
import { logger } from '@/lib/logger';

export interface PrivacyConsent {
  id?: string;
  user_id: string;
  consent_type: string;
  consented: boolean;
  created_at?: string;
  updated_at?: string;
}

/**
 * Create new privacy consent table migration
 * @returns Object with success status or error information
 */
export async function createPrivacyConsentTable(): Promise<
  | { success: true; message?: string }
  | { error: string; adminAction?: boolean; details?: string }
> {
  try {
    const supabase = await createServerActionClient();
    const supabaseAny = asUnknown(supabase) as ExtendedSupabaseClient;

    // First check if the tables already exist
    const { data: tablesExist, error: checkError }: SqlExecResult = await supabaseAny.rpc('exec_sql', {
      sql: `SELECT table_name FROM information_schema.tables
            WHERE table_schema = 'public'
            AND table_name IN ('privacy_consent_versions', 'privacy_consents')`
    });

    if (checkError) {
      console.error("Error checking tables:", checkError);
      return { error: `Error checking if tables exist: ${checkError.message}` };
    }

    // If tables already exist, return success
    if (tablesExist && Array.isArray(tablesExist) && tablesExist.length === 2) {
      return { success: true, message: "Privacy tables already exist" };
    }

    // Try to create the tables using the RPC function
    const { error: rpcError }: SqlExecResult = await supabaseAny.rpc('exec_sql', {
      sql: `
        -- Create extension if it doesn't exist
        CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

        -- Create privacy_consent_versions table
        CREATE TABLE IF NOT EXISTS privacy_consent_versions (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          version VARCHAR(50) NOT NULL,
          name VARCHAR(100) NOT NULL,
          description TEXT,
          consent_text TEXT NOT NULL,
          effective_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
          expiry_date TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );

        -- Create privacy_consents table with simplified structure
        CREATE TABLE IF NOT EXISTS privacy_consents (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id TEXT NOT NULL,
          consent_type VARCHAR(50) NOT NULL,
          consent_given BOOLEAN NOT NULL DEFAULT false,
          consent_version VARCHAR(50) NOT NULL,
          consent_text TEXT NOT NULL,
          expires_at TIMESTAMP WITH TIME ZONE,
          ip_address VARCHAR(45),
          user_agent TEXT,
          previous_record_id UUID,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );

        -- Create index for faster lookups
        CREATE INDEX IF NOT EXISTS idx_privacy_consents_user_id ON privacy_consents(user_id);
        CREATE INDEX IF NOT EXISTS idx_privacy_consents_type ON privacy_consents(consent_type);
      `
    });

    if (rpcError) {
      console.error("Error creating privacy tables:", rpcError);

      // If the function doesn't exist, try a direct SQL approach
      if (rpcError.message.includes('does not exist')) {
        logger.info("Attempting direct SQL creation of privacy tables");
        const result = await setupPrivacyTables(supabase as ExtendedSupabaseClient);
        return result;
      }

      return {
        error: `Failed to create privacy tables: ${rpcError.message}`,
        adminAction: true,
        details: `The database returned: ${rpcError.message}\n\nPlease contact a database administrator to create the required tables.`
      };
    }

    return { success: true };
  } catch (error) {
    console.error("Error in createPrivacyConsentTable:", error);
    return {
      error: error instanceof Error ? error.message : "Unknown error",
      adminAction: true,
      details: `An unexpected error occurred: ${error instanceof Error ? error.message : "Unknown error"}`
    };
  }
}

/**
 * Setup privacy tables using direct SQL
 */
async function setupPrivacyTables(supabase: ExtendedSupabaseClient): Promise<
  | { success: true; message?: string }
  | { error: string; adminAction?: boolean; details?: string }
> {
  try {
    // Create tables with direct SQL
    const { error: sqlError } = await supabase.rpc('exec_sql', {
      sql: `
        -- Create extension if it doesn't exist
        CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

        -- Create privacy_consent_versions table
        CREATE TABLE IF NOT EXISTS privacy_consent_versions (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          version VARCHAR(50) NOT NULL,
          name VARCHAR(100) NOT NULL,
          description TEXT,
          consent_text TEXT NOT NULL,
          effective_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
          expiry_date TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );

        -- Create privacy_consents table with simplified structure
        CREATE TABLE IF NOT EXISTS privacy_consents (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id TEXT NOT NULL,
          consent_type VARCHAR(50) NOT NULL,
          consent_given BOOLEAN NOT NULL DEFAULT false,
          consent_version VARCHAR(50) NOT NULL,
          consent_text TEXT NOT NULL,
          expires_at TIMESTAMP WITH TIME ZONE,
          ip_address VARCHAR(45),
          user_agent TEXT,
          previous_record_id UUID,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );

        -- Create index for faster lookups
        CREATE INDEX IF NOT EXISTS idx_privacy_consents_user_id ON privacy_consents(user_id);
        CREATE INDEX IF NOT EXISTS idx_privacy_consents_type ON privacy_consents(consent_type);
      `
    });

    if (sqlError) {
      console.error("Error creating tables with SQL:", sqlError);
      return {
        error: `Failed to create privacy tables: ${sqlError.message}`,
        adminAction: true,
        details: `The database returned: ${sqlError.message}\n\nPlease contact a database administrator to create the required tables.`
      };
    }

    // Insert initial consent versions
    const { error: insertError } = await supabase.rpc('exec_sql', {
      sql: `
        -- Insert initial consent versions if they don't exist
        INSERT INTO privacy_consent_versions (version, name, description, consent_text)
        SELECT '1.0', 'Marketing', 'Receive marketing communications and updates', 'I agree to receive marketing communications, newsletters, and updates about events and services.'
        WHERE NOT EXISTS (SELECT 1 FROM privacy_consent_versions WHERE name = 'Marketing');

        INSERT INTO privacy_consent_versions (version, name, description, consent_text)
        SELECT '1.0', 'Analytics', 'Allow usage data collection for analytics', 'I agree to the collection and processing of my usage data for analytics and service improvement purposes.'
        WHERE NOT EXISTS (SELECT 1 FROM privacy_consent_versions WHERE name = 'Analytics');

        INSERT INTO privacy_consent_versions (version, name, description, consent_text)
        SELECT '1.0', 'Third Party', 'Share data with trusted third parties', 'I agree to share my data with trusted third-party partners for service delivery and improvement.'
        WHERE NOT EXISTS (SELECT 1 FROM privacy_consent_versions WHERE name = 'Third Party');

        INSERT INTO privacy_consent_versions (version, name, description, consent_text)
        SELECT '1.0', 'Data Sharing', 'Share profile data with event organizers', 'I agree to share my profile information with event organizers for event registration and management.'
        WHERE NOT EXISTS (SELECT 1 FROM privacy_consent_versions WHERE name = 'Data Sharing');

        INSERT INTO privacy_consent_versions (version, name, description, consent_text)
        SELECT '1.0', 'Profile Display', 'Display profile in public listings', 'I agree to display my profile information in public event attendee listings.'
        WHERE NOT EXISTS (SELECT 1 FROM privacy_consent_versions WHERE name = 'Profile Display');
      `
    });

    if (insertError) {
      console.error("Error inserting initial consent versions:", insertError);
      return {
        error: `Failed to insert initial consent versions: ${insertError.message}`,
        adminAction: true,
        details: `The database returned: ${insertError.message}\n\nPlease contact a database administrator to complete the setup.`
      };
    }

    return { success: true };
  } catch (error) {
    console.error("Error in setupPrivacyTables:", error);
    return {
      error: error instanceof Error ? error.message : "Unknown error",
      adminAction: true,
      details: `An unexpected error occurred: ${error instanceof Error ? error.message : "Unknown error"}`
    };
  }
}

/**
 * Get all consent records for the current user
 */
export async function getUserConsents() {
  try {
    const supabase = await createServerActionClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user?.id) {
      console.warn("No user ID found in getUserConsents");
      return { error: "Unauthorized", consents: [] };
    }

    const userId = user.id;

    // Try to directly query the table instead of checking if it exists first
    const { data, error } = await supabase
      .from('privacy_consents')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      // If the error is that the table doesn't exist, handle it gracefully
      if (error.code === '42P01' || error.message.includes('does not exist')) {
        console.warn("privacy_consents table does not exist");
        return { error: "Privacy consents table does not exist", consents: [] };
      }

      console.error("Error in getUserConsents query:", error);
      return { error: `Error fetching consents: ${error.message}`, consents: [] };
    }

    return { consents: data || [] };
  } catch (error) {
    console.error("Exception in getUserConsents:", error);
    return { error: error instanceof Error ? error.message : "Unknown error", consents: [] };
  }
}

/**
 * Get a consent by type for the current user (most recent version)
 */
export async function getUserConsentByType(consentType: string) {
  try {
    const supabase = await createServerActionClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user?.id) {
      return { error: "Unauthorized" };
    }

    const userId = user.id;

    // Try to directly query the table
    const { data, error } = await supabase
      .from('privacy_consents')
      .select('*')
      .eq('user_id', userId)
      .eq('consent_type', consentType)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error) {
      // Not found error is fine
      if (error.code === 'PGRST116') {
        return { consent: null };
      }

      // If the error is that the table doesn't exist, handle it gracefully
      if (error.code === '42P01' || error.message.includes('does not exist')) {
        return { error: "Privacy consents table does not exist" };
      }

      return { error: `Error fetching consent: ${error.message}` };
    }

    return { consent: data };
  } catch (error) {
    console.error("Error in getUserConsentByType:", error);
    return { error: error instanceof Error ? error.message : "Unknown error" };
  }
}

/**
 * Create a new privacy consent record
 */
export async function createConsent(formData: ConsentFormData, requestInfo?: { ip?: string, userAgent?: string }) {
  try {
    const supabase = await createServerActionClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.user?.id) {
      return { error: "Unauthorized" };
    }

    const userId = session.user.id;

    // Validate form data
    const validationResult = validateConsentData(formData, userId);
    if (!validationResult.success) {
      return { error: validationResult.error };
    }

    // Supabase client already created above

    const { data, error } = await supabase
      .from('privacy_consents')
      .insert({
        user_id: userId,
        consent_type: formData.consentType,
        consent_given: formData.consentGiven,
        consent_version: formData.consentVersion,
        consent_text: formData.consentText,
        expires_at: formData.expiresAt || null,
        ip_address: requestInfo?.ip || null,
        user_agent: requestInfo?.userAgent || null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      console.error("Error creating consent:", error);
      return { error: `Error creating consent: ${error.message}` };
    }

    revalidatePath('/dashboard/profile');
    return { consent: data };
  } catch (error) {
    console.error("Error in createConsent:", error);
    return { error: error instanceof Error ? error.message : "Unknown error" };
  }
}

/**
 * Update a consent record (creates a new record with updated values)
 */
export async function updateConsent(consentId: string, formData: ConsentFormData, requestInfo?: { ip?: string, userAgent?: string }) {
  try {
    const supabase = await createServerActionClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.user?.id) {
      return { error: "Unauthorized" };
    }

    const userId = session.user.id;

    // Validate form data
    const validationResult = validateConsentData(formData, userId);
    if (!validationResult.success) {
      return { error: validationResult.error };
    }

    // Supabase client already created above

    // First verify the consent record belongs to the user
    const { data: existingConsent, error: fetchError } = await supabase
      .from('privacy_consents')
      .select('id, consent_type, consent_version')
      .eq('id', consentId)
      .eq('user_id', userId)
      .single();

    if (fetchError || !existingConsent) {
      return { error: "Consent record not found or you don't have permission to update it" };
    }

    // For GDPR compliance, we create a new record rather than updating the existing one
    const { data, error } = await supabase
      .from('privacy_consents')
      .insert({
        user_id: userId,
        consent_type: formData.consentType,
        consent_given: formData.consentGiven,
        consent_version: formData.consentVersion,
        consent_text: formData.consentText,
        expires_at: formData.expiresAt || null,
        ip_address: requestInfo?.ip || null,
        user_agent: requestInfo?.userAgent || null,
        previous_record_id: consentId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      console.error("Error updating consent:", error);
      return { error: `Error updating consent: ${error.message}` };
    }

    revalidatePath('/dashboard/profile');
    return { consent: data };
  } catch (error) {
    console.error("Error in updateConsent:", error);
    return { error: error instanceof Error ? error.message : "Unknown error" };
  }
}

/**
 * Validate consent form data
 */
function validateConsentData(formData: ConsentFormData, userId: string) {
  try {
    // Use a more lenient validation approach for development
    if (!formData.consentType) {
      return { success: false, error: "Consent type is required" };
    }

    if (typeof formData.consentGiven !== 'boolean') {
      return { success: false, error: "Consent given must be boolean" };
    }

    if (!formData.consentVersion) {
      return { success: false, error: "Consent version is required" };
    }

    if (!formData.consentText) {
      return { success: false, error: "Consent text is required" };
    }

    return { success: true };
  } catch (error) {
    console.error("Error validating consent data:", error);
    return { success: false, error: "Validation failed" };
  }
}

/**
 * Get all consent versions available in the system
 */
export async function getConsentVersions() {
  try {
    const supabase = await createServerActionClient();

    // Try to directly query the table instead of checking if it exists first
    // This approach will be more reliable since the exec_sql check might be returning false negatives
    const { data, error } = await supabase
      .from('privacy_consent_versions')
      .select('*')
      .order('effective_date', { ascending: false });

    if (error) {
      // If the error is that the table doesn't exist, handle it gracefully
      if (error.code === '42P01' || error.message.includes('does not exist')) {
        console.warn("privacy_consent_versions table does not exist");
        return { error: "Privacy consent versions table does not exist" };
      }

      console.error("Error fetching consent versions:", error);
      return { error: `Error fetching consent versions: ${error.message}` };
    }

    return { versions: data || [] };
  } catch (error) {
    console.error("Error in getConsentVersions:", error);
    return { error: error instanceof Error ? error.message : "Unknown error" };
  }
}

/**
 * Force refresh the privacy schema setup
 * This can be used when there are schema cache issues
 */
export async function refreshPrivacySchema(): Promise<
  | { success: true; message?: string }
  | { error: string; adminAction?: boolean; details?: string }
> {
  try {
    const supabase = await createServerActionClient();
    const supabaseAny = asUnknown(supabase) as ExtendedSupabaseClient;

    // Attempt to refresh the schema cache
    const { error: refreshError } = await supabaseAny.rpc('exec_sql', {
      sql: `
        -- Drop existing tables if they exist
        DROP TABLE IF EXISTS privacy_consents;
        DROP TABLE IF EXISTS privacy_consent_versions;

        -- Now recreate them with the correct schema
        -- Create extension if it doesn't exist
        CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

        -- Create privacy_consent_versions table
        CREATE TABLE IF NOT EXISTS privacy_consent_versions (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          version VARCHAR(50) NOT NULL,
          name VARCHAR(100) NOT NULL,
          description TEXT,
          consent_text TEXT NOT NULL,
          effective_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
          expiry_date TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );

        -- Create privacy_consents table
        CREATE TABLE IF NOT EXISTS privacy_consents (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id TEXT NOT NULL,
          consent_type VARCHAR(50) NOT NULL,
          consent_given BOOLEAN NOT NULL DEFAULT false,
          consent_version VARCHAR(50) NOT NULL,
          consent_text TEXT NOT NULL,
          expires_at TIMESTAMP WITH TIME ZONE,
          ip_address VARCHAR(45),
          user_agent TEXT,
          previous_record_id UUID,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );

        -- Create index for faster lookups
        CREATE INDEX IF NOT EXISTS idx_privacy_consents_user_id ON privacy_consents(user_id);
        CREATE INDEX IF NOT EXISTS idx_privacy_consents_type ON privacy_consents(consent_type);

        -- Force schema cache refresh
        COMMENT ON TABLE privacy_consents IS 'Table for storing user privacy consent records';
        COMMENT ON COLUMN privacy_consents.consent_given IS 'Whether the user has given consent';
      `
    });

    if (refreshError) {
      console.error("Error refreshing privacy schema:", refreshError);
      return {
        error: `Failed to refresh privacy schema: ${refreshError.message}`,
        adminAction: true,
        details: `The database returned: ${refreshError.message}\n\nPlease contact a database administrator.`
      };
    }

    // Insert initial consent versions
    const { error: insertError } = await supabaseAny.rpc('exec_sql', {
      sql: `
        -- Insert initial consent versions if they don't exist
        INSERT INTO privacy_consent_versions (version, name, description, consent_text)
        SELECT '1.0', 'Marketing', 'Receive marketing communications and updates', 'I agree to receive marketing communications, newsletters, and updates about events and services.'
        WHERE NOT EXISTS (SELECT 1 FROM privacy_consent_versions WHERE name = 'Marketing');

        INSERT INTO privacy_consent_versions (version, name, description, consent_text)
        SELECT '1.0', 'Analytics', 'Allow usage data collection for analytics', 'I agree to the collection and processing of my usage data for analytics and service improvement purposes.'
        WHERE NOT EXISTS (SELECT 1 FROM privacy_consent_versions WHERE name = 'Analytics');

        INSERT INTO privacy_consent_versions (version, name, description, consent_text)
        SELECT '1.0', 'Third Party', 'Share data with trusted third parties', 'I agree to share my data with trusted third-party partners for service delivery and improvement.'
        WHERE NOT EXISTS (SELECT 1 FROM privacy_consent_versions WHERE name = 'Third Party');

        INSERT INTO privacy_consent_versions (version, name, description, consent_text)
        SELECT '1.0', 'Data Sharing', 'Share profile data with event organizers', 'I agree to share my profile information with event organizers for event registration and management.'
        WHERE NOT EXISTS (SELECT 1 FROM privacy_consent_versions WHERE name = 'Data Sharing');

        INSERT INTO privacy_consent_versions (version, name, description, consent_text)
        SELECT '1.0', 'Profile Display', 'Display profile in public listings', 'I agree to display my profile information in public event attendee listings.'
        WHERE NOT EXISTS (SELECT 1 FROM privacy_consent_versions WHERE name = 'Profile Display');

        INSERT INTO privacy_consent_versions (version, name, description, consent_text)
        SELECT '1.0', 'Cookie Preferences', 'Control cookie settings', 'I agree to allow non-essential cookies to be stored on my device to enhance site functionality and personalization.'
        WHERE NOT EXISTS (SELECT 1 FROM privacy_consent_versions WHERE name = 'Cookie Preferences');
      `
    });

    if (insertError) {
      console.error("Error inserting initial consent versions:", insertError);
      return {
        error: `Failed to insert initial consent versions: ${insertError.message}`
      };
    }

    return { success: true, message: "Privacy schema refreshed successfully" };
  } catch (error) {
    console.error("Error in refreshPrivacySchema:", error);
    return {
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
}

/**
 * Create the privacy consent table if it doesn't exist
 */
export async function ensurePrivacyConsentTable() {
  const supabase = await createClient();
  const supabaseAny = asUnknown(supabase) as ExtendedSupabaseClient;

  try {
    // Check if table exists
    const tableCheckResult: SqlExecResult = await supabaseAny.rpc("exec_sql", {
      sql: `
        SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_schema = 'public'
          AND table_name = 'privacy_consents'
        ) as exists
      `,
    });

    // Create table if it doesn't exist
    if (!tableCheckResult.data || tableCheckResult.data.length === 0 ||
      (tableCheckResult.data[0] && !tableCheckResult.data[0].exists)) {
      const createTableResult: SqlExecResult = await supabaseAny.rpc("exec_sql", {
        sql: `
          CREATE TABLE IF NOT EXISTS public.privacy_consents (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id UUID NOT NULL,
            consent_type TEXT NOT NULL,
            consented BOOLEAN NOT NULL DEFAULT false,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            UNIQUE(user_id, consent_type)
          );

          -- Add RLS policies
          ALTER TABLE public.privacy_consents ENABLE ROW LEVEL SECURITY;

          -- Policy for users to see only their own consents
          CREATE POLICY "Users can view their own privacy consents"
            ON public.privacy_consents
            FOR SELECT
            USING (auth.uid() = user_id);

          -- Policy for users to insert their own consents
          CREATE POLICY "Users can insert their own privacy consents"
            ON public.privacy_consents
            FOR INSERT
            WITH CHECK (auth.uid() = user_id);

          -- Policy for users to update their own privacy consents
          CREATE POLICY "Users can update their own privacy consents"
            ON public.privacy_consents
            FOR UPDATE
            USING (auth.uid() = user_id);
        `,
      });

      return { success: true };
    }

    return { success: true };
  } catch (error) {
    console.error("Error ensuring privacy consent table:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}