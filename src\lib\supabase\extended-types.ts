import { Database } from '@/types/supabase';
import { SupabaseClient } from '@supabase/supabase-js';

export type Tables = Database['public']['Tables'];
export type TableName = keyof Tables & string;

// Helper type to convert unknown to any safely
export const asAny = <T>(value: unknown): T => value as T;

// Type-safe table access
export type TypedSupabaseClient = SupabaseClient<Database>;

// Helper types for table operations
export type TableRow<T extends TableName> = Tables[T]['Row'];
export type TableInsert<T extends TableName> = Tables[T]['Insert'];
export type TableUpdate<T extends TableName> = Tables[T]['Update'];

// Type guard for checking if a table name exists
export const isValidTableName = (name: string): name is TableName => {
  return name in ({} as Tables);
};

// Type guard for checking if a value matches a table's row type
export const isTableRow = <T extends TableName>(
  tableName: T,
  value: unknown
): value is TableRow<T> => {
  return typeof value === 'object' && value !== null;
};

// Helper function to ensure type safety when accessing tables
export const getTypedTable = <T extends TableName>(
  client: TypedSupabaseClient,
  tableName: T
) => {
  return client.from(tableName);
};

// Helper type for query filters
export type QueryFilter<T extends TableName> = Partial<Record<keyof TableRow<T>, unknown>>;

/**
 * Result type for the exec_sql RPC function
 */
export interface SqlExecResult {
  data: Record<string, unknown>[] | null;
  error: { message: string } | null;
}

/**
 * Extended database interface to add custom tables & functions
 */
export interface ExtendedDatabase extends Database {
  public: Database['public'] & {
    Tables: Database['public']['Tables'] & {
      schema_migrations: {
        Row: {
          id: string;
          name: string;
          applied_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          applied_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          applied_at?: string;
        };
      };
    };
    Functions: Database['public']['Functions'] & {
      pg_notify: {
        Args: {
          channel: string;
          payload: string;
        };
        Returns: void;
      };
    };
  };
}

/**
 * Extended Supabase client type that includes custom RPC methods
 */
export type ExtendedSupabaseClient = SupabaseClient & {
  rpc: (procedure: string, params?: { [key: string]: unknown }) => Promise<{
    data: unknown;
    error: { message: string } | null;
  }>;
};

/**
 * Custom Supabase client type with our extended database
 */
export type CustomSupabaseClient = SupabaseClient<ExtendedDatabase>;

/**
 * Helper function to cast Supabase object to unknown to bypass type issues
 * Use this only when you need to access tables or functions not properly typed
 */
export function asUnknown<T>(obj: T): unknown {
  return obj as unknown;
}