#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Automated Lint Error Fixer
 * Systematically fixes common parsing and formatting errors
 */

// Common patterns to fix
const fixes = [
    // HTML entities in JSX attributes
    {
        pattern: /&quot;/g,
        replacement: '"',
        description: 'Replace HTML quote entities with regular quotes'
    },
    {
        pattern: /&apos;/g,
        replacement: "'",
        description: 'Replace HTML apostrophe entities with regular apostrophes'
    },
    {
        pattern: /&lt;/g,
        replacement: '<',
        description: 'Replace HTML less-than entities'
    },
    {
        pattern: /&gt;/g,
        replacement: '>',
        description: 'Replace HTML greater-than entities'
    },
    // Syntax errors
    {
        pattern: /\(\)\s*=\/>\s*/g,
        replacement: '() => ',
        description: 'Fix malformed arrow function syntax'
    },
    {
        pattern: /\s*=\/>\s*/g,
        replacement: ' => ',
        description: 'Fix malformed arrow function syntax in callbacks'
    },
    // Common TypeScript issues
    {
        pattern: /\s+as\s+T\[\]/g,
        replacement: ' as T[]',
        description: 'Fix type casting spacing'
    },
    {
        pattern: /\s+as\s+T,/g,
        replacement: ' as T',
        description: 'Fix type casting with comma issues'
    }
];

// File extensions to process
const extensions = ['.tsx', '.ts', '.jsx', '.js'];

// Directories to exclude
const excludeDirs = ['node_modules', '.next', '.git', 'dist', 'build'];

/**
 * Recursively get all files with specific extensions
 */
function getFiles(dir, fileList = []) {
    const files = fs.readdirSync(dir);

    files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);

        if (stat.isDirectory()) {
            // Skip excluded directories
            if (!excludeDirs.includes(file)) {
                getFiles(filePath, fileList);
            }
        } else {
            // Check if file has target extension
            const ext = path.extname(file);
            if (extensions.includes(ext)) {
                fileList.push(filePath);
            }
        }
    });

    return fileList;
}

/**
 * Apply fixes to a single file
 */
function fixFile(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        let modified = false;
        const appliedFixes = [];

        // Apply each fix
        fixes.forEach(fix => {
            const beforeCount = (content.match(fix.pattern) || []).length;
            if (beforeCount > 0) {
                content = content.replace(fix.pattern, fix.replacement);
                appliedFixes.push(`${fix.description} (${beforeCount} instances)`);
                modified = true;
            }
        });

        // Write back if modified
        if (modified) {
            fs.writeFileSync(filePath, content, 'utf8');
            console.log(`✅ Fixed: ${filePath}`);
            appliedFixes.forEach(fix => console.log(`   - ${fix}`));
        }

        return modified;
    } catch (error) {
        console.error(`❌ Error processing ${filePath}:`, error.message);
        return false;
    }
}

/**
 * Main execution
 */
function main() {
    console.log('🚀 Starting automated lint error fixes...\n');

    // Get all target files
    const srcDir = path.join(process.cwd(), 'src');
    const files = getFiles(srcDir);

    console.log(`📁 Found ${files.length} files to process\n`);

    let fixedCount = 0;
    let totalFiles = 0;

    // Process each file
    files.forEach(filePath => {
        totalFiles++;
        const wasFixed = fixFile(filePath);
        if (wasFixed) {
            fixedCount++;
        }
    });

    console.log('\n📊 Summary:');
    console.log(`   Total files processed: ${totalFiles}`);
    console.log(`   Files modified: ${fixedCount}`);
    console.log(`   Files unchanged: ${totalFiles - fixedCount}`);

    if (fixedCount > 0) {
        console.log('\n🎉 Automation complete! Run "pnpm lint" to check remaining issues.');
    } else {
        console.log('\n✨ No automatic fixes needed!');
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = { fixFile, getFiles, fixes }; 