export type Json =
    | string
    | number
    | boolean
    | null
    | { [key: string]: Json | undefined }
    | Json[]

export type Database = {
    public: {
        Tables: {
            admin_users: {
                Row: {
                    created_at: string | null
                    id: string
                }
                Insert: {
                    created_at?: string | null
                    id: string
                }
                Update: {
                    created_at?: string | null
                    id?: string
                }
                Relationships: []
            }
            collections: {
                Row: {
                    collected_at: string | null
                    collected_by: string | null
                    created_at: string
                    id: string
                    notes: string | null
                    registration_id: string
                    status: string
                    type: string
                    updated_at: string | null
                }
                Insert: {
                    collected_at?: string | null
                    collected_by?: string | null
                    created_at?: string
                    id: string
                    notes?: string | null
                    registration_id: string
                    status?: string
                    type: string
                    updated_at?: string | null
                }
                Update: {
                    collected_at?: string | null
                    collected_by?: string | null
                    created_at?: string
                    id?: string
                    notes?: string | null
                    registration_id?: string
                    status?: string
                    type?: string
                    updated_at?: string | null
                }
                Relationships: [
                    {
                        foreignKeyName: "collections_collected_by_users_id_fk"
                        columns: ["collected_by"]
                        isOneToOne: false
                        referencedRelation: "users"
                        referencedColumns: ["id"]
                    },
                    {
                        foreignKeyName: "collections_registration_id_registrations_id_fk"
                        columns: ["registration_id"]
                        isOneToOne: false
                        referencedRelation: "registrations"
                        referencedColumns: ["id"]
                    }
                ]
            }
            events: {
                Row: {
                    id: string
                    title: string
                    description: string | null
                    location: string | null
                    start_date: string
                    end_date: string
                    status: 'draft' | 'published' | 'cancelled'
                    user_id: string
                    organization_id: string | null
                    created_at: string
                    updated_at: string
                }
                Insert: {
                    id?: string
                    title: string
                    description?: string | null
                    location?: string | null
                    start_date: string
                    end_date: string
                    status?: 'draft' | 'published' | 'cancelled'
                    user_id: string
                    organization_id?: string | null
                    created_at?: string
                    updated_at?: string
                }
                Update: {
                    id?: string
                    title?: string
                    description?: string | null
                    location?: string | null
                    start_date?: string
                    end_date?: string
                    status?: 'draft' | 'published' | 'cancelled'
                    user_id?: string
                    organization_id?: string | null
                    created_at?: string
                    updated_at?: string
                }
                Relationships: [
                    {
                        foreignKeyName: "events_user_id_fkey"
                        columns: ["user_id"]
                        isOneToOne: false
                        referencedRelation: "users"
                        referencedColumns: ["id"]
                    },
                    {
                        foreignKeyName: "events_organization_id_fkey"
                        columns: ["organization_id"]
                        isOneToOne: false
                        referencedRelation: "organizations"
                        referencedColumns: ["id"]
                    }
                ]
            }
            organizations: {
                Row: {
                    id: string
                    name: string
                    logo_url: string | null
                    created_at: string
                    updated_at: string
                }
                Insert: {
                    id?: string
                    name: string
                    logo_url?: string | null
                    created_at?: string
                    updated_at?: string
                }
                Update: {
                    id?: string
                    name?: string
                    logo_url?: string | null
                    created_at?: string
                    updated_at?: string
                }
                Relationships: []
            }
            organization_members: {
                Row: {
                    id: string
                    organization_id: string
                    user_id: string
                    role: string
                    created_at: string
                    updated_at: string
                }
                Insert: {
                    id?: string
                    organization_id: string
                    user_id: string
                    role?: string
                    created_at?: string
                    updated_at?: string
                }
                Update: {
                    id?: string
                    organization_id?: string
                    user_id?: string
                    role?: string
                    created_at?: string
                    updated_at?: string
                }
                Relationships: [
                    {
                        foreignKeyName: "organization_members_organization_id_fkey"
                        columns: ["organization_id"]
                        isOneToOne: false
                        referencedRelation: "organizations"
                        referencedColumns: ["id"]
                    },
                    {
                        foreignKeyName: "organization_members_user_id_fkey"
                        columns: ["user_id"]
                        isOneToOne: false
                        referencedRelation: "users"
                        referencedColumns: ["id"]
                    }
                ]
            }
            webhook_logs: {
                Row: {
                    created_at: string | null
                    id: string
                    payload: Json
                    type: string
                }
                Insert: {
                    created_at?: string | null
                    id?: string
                    payload: Json
                    type: string
                }
                Update: {
                    created_at?: string | null
                    id?: string
                    payload?: Json
                    type?: string
                }
                Relationships: []
            }
            users: {
                Row: {
                    address: string | null
                    apartment: string | null
                    auth_user_id: string | null
                    avatar: string | null
                    bio: string | null
                    city: string | null
                    contactNo: string | null
                    country: string | null
                    created_at: string
                    dateOfBirth: string | null
                    email: string
                    emergencyContactName: string | null
                    emergencyContactNo: string | null
                    emergencyContactRelationship: string | null
                    eventCategories: Json | null
                    external_id: string | null
                    first_name: string
                    gender: string | null
                    ic: string | null
                    id: string
                    ispublic: boolean | null
                    isPublic: boolean | null
                    last_clerk_sync: string | null
                    last_name: string | null
                    nationality: string | null
                    passport: string | null
                    postcode: string | null
                    role: string
                    state: string | null
                    tshirt_size: string | null
                    tshirtSize: string | null
                    updated_at: string | null
                    username: string | null
                }
                Insert: {
                    address?: string | null
                    apartment?: string | null
                    auth_user_id?: string | null
                    avatar?: string | null
                    bio?: string | null
                    city?: string | null
                    contactNo?: string | null
                    country?: string | null
                    created_at?: string
                    dateOfBirth?: string | null
                    email: string
                    emergencyContactName?: string | null
                    emergencyContactNo?: string | null
                    emergencyContactRelationship?: string | null
                    eventCategories?: Json | null
                    external_id?: string | null
                    first_name: string
                    gender?: string | null
                    ic?: string | null
                    id: string
                    ispublic?: boolean | null
                    isPublic?: boolean | null
                    last_clerk_sync?: string | null
                    last_name?: string | null
                    nationality?: string | null
                    passport?: string | null
                    postcode?: string | null
                    role?: string
                    state?: string | null
                    tshirt_size?: string | null
                    tshirtSize?: string | null
                    updated_at?: string | null
                    username?: string | null
                }
                Update: {
                    address?: string | null
                    apartment?: string | null
                    auth_user_id?: string | null
                    avatar?: string | null
                    bio?: string | null
                    city?: string | null
                    contactNo?: string | null
                    country?: string | null
                    created_at?: string
                    dateOfBirth?: string | null
                    email?: string
                    emergencyContactName?: string | null
                    emergencyContactNo?: string | null
                    emergencyContactRelationship?: string | null
                    eventCategories?: Json | null
                    external_id?: string | null
                    first_name?: string
                    gender?: string | null
                    ic?: string | null
                    id?: string
                    ispublic?: boolean | null
                    isPublic?: boolean | null
                    last_clerk_sync?: string | null
                    last_name?: string | null
                    nationality?: string | null
                    passport?: string | null
                    postcode?: string | null
                    role?: string
                    state?: string | null
                    tshirt_size?: string | null
                    tshirtSize?: string | null
                    updated_at?: string | null
                    username?: string | null
                }
                Relationships: []
            }
        }
        Views: {
            [_ in never]: never
        }
        Functions: {
            clerk_id_to_uuid: {
                Args: { clerk_id: string }
                Returns: string
            }
        }
        Enums: {
            [_ in never]: never
        }
        CompositeTypes: {
            [_ in never]: never
        }
    }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
    DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
    TableName extends DefaultSchemaTableNameOrOptions extends {
        schema: keyof Database
    }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
    ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
            Row: infer R
        }
    ? R
    : never
    : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
            Row: infer R
        }
    ? R
    : never
    : never

export type TablesInsert<
    DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
    TableName extends DefaultSchemaTableNameOrOptions extends {
        schema: keyof Database
    }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
    ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
        Insert: infer I
    }
    ? I
    : never
    : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
    }
    ? I
    : never
    : never

export type TablesUpdate<
    DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
    TableName extends DefaultSchemaTableNameOrOptions extends {
        schema: keyof Database
    }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
    ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
        Update: infer U
    }
    ? U
    : never
    : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
    }
    ? U
    : never
    : never

export type Enums<
    DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
    EnumName extends DefaultSchemaEnumNameOrOptions extends {
        schema: keyof Database
    }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
    ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
    : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
    PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
    CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
        schema: keyof Database
    }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
    ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
    : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
    public: {
        Enums: {},
    },
} as const 