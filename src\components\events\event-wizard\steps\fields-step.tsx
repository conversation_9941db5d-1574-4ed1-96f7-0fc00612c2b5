/**
 * Fields step component for event wizard
 * @module components/events/event-wizard/steps/fields-step
 */

'use client';

import { useEffect, useState } from 'react';
import { useWizard } from '../wizard-container';
import { useFieldStore } from '@/stores/field-store';
import {
  FieldType,
  type EventField,
  type TextField,
  type NumberField,
  type SelectField,
  type MultiSelectField,
  type CheckboxField,
  type DateField,
  type FileField,
  type DisplayField
} from '@/types/event/field-types';
import { FieldFactory } from '@/lib/field-factory';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { FormField } from '@/components/ui/form-field';
import { DragDropContext, Droppable, Draggable, type DropResult } from '@hello-pangea/dnd';

// Helper function to convert field value to string
const fieldValueToString = (value: unknown): string => {
  if (value === null || value === undefined) return '';
  return String(value);
};

// Helper function to convert field value based on type
const convertFieldValue = (value: string, field: EventField): string | number | string[] | undefined => {
  if (!value) return undefined;

  switch (field.type) {
    case FieldType.Number:
      const num = Number(value);
      return isNaN(num) ? undefined : num;
    case FieldType.MultiSelect:
    case FieldType.Checkbox:
      return value.split(',').map(v => v.trim());
    default:
      return value;
  }
};

// Helper function to ensure field has required validation rules
const ensureValidationRules = (field: EventField): EventField => {
  if (field.type === FieldType.Number && !field.validationRules) {
    return {
      ...field,
      validationRules: {
        type: 'number',
        min: null,
        max: null,
        step: null,
        customMessage: null
      }
    } as NumberField;
  }
  return field;
};

// Helper function to validate field against schema
const validateField = (field: EventField): EventField => {
  const baseField = {
    id: field.id,
    label: field.label,
    description: field.description,
    placeholder: field.placeholder,
    required: field.required,
    order: field.order
  };

  switch (field.type) {
    case FieldType.NUMBER:
      return {
        ...baseField,
        type: FieldType.NUMBER,
        defaultValue: (field as NumberField).defaultValue,
        validationRules: field.validationRules || {
          type: 'number',
          min: null,
          max: null,
          step: null,
          customMessage: null
        }
      } as NumberField;

    case FieldType.SELECT:
    case FieldType.RADIO:
      return {
        ...baseField,
        type: field.type,
        options: Array.isArray((field as SelectField).options) ? (field as SelectField).options : [],
        defaultValue: (field as SelectField).defaultValue,
        validationRules: null
      } as SelectField;

    case FieldType.MULTISELECT:
      return {
        ...baseField,
        type: FieldType.MULTISELECT,
        options: Array.isArray((field as MultiSelectField).options) ? (field as MultiSelectField).options : [],
        defaultValue: (field as MultiSelectField).defaultValue,
        validationRules: null
      } as MultiSelectField;

    case FieldType.CHECKBOX:
      return {
        ...baseField,
        type: FieldType.CHECKBOX,
        defaultValue: (field as CheckboxField).defaultValue,
        validationRules: null
      } as CheckboxField;

    case FieldType.TEXT:
    case FieldType.TEXTAREA:
    case FieldType.EMAIL:
    case FieldType.PHONE:
      return {
        ...baseField,
        type: field.type,
        defaultValue: (field as TextField).defaultValue,
        validationRules: field.validationRules || {
          type: 'text',
          minLength: null,
          maxLength: null,
          pattern: null,
          customMessage: null
        }
      } as TextField;

    case FieldType.DATE:
    case FieldType.TIME:
    case FieldType.DATETIME:
      return {
        ...baseField,
        type: field.type,
        defaultValue: (field as DateField).defaultValue,
        validationRules: field.validationRules || {
          type: 'date',
          min: null,
          max: null,
          customMessage: null
        }
      } as DateField;

    case FieldType.FILE:
    case FieldType.IMAGE:
      return {
        ...baseField,
        type: field.type,
        defaultValue: (field as FileField).defaultValue,
        validationRules: field.validationRules || {
          type: 'file',
          maxSize: null,
          allowedTypes: null,
          customMessage: null
        }
      } as FileField;

    case FieldType.DISPLAY:
      return {
        ...baseField,
        type: FieldType.DISPLAY,
        content: (field as DisplayField).content || '',
        validationRules: null
      } as DisplayField;

    default:
      return field;
  }
};

const fieldTypes = [
  { value: FieldType.TEXT, label: 'Text' },
  { value: FieldType.TEXTAREA, label: 'Text Area' },
  { value: FieldType.EMAIL, label: 'Email' },
  { value: FieldType.PHONE, label: 'Phone' },
  { value: FieldType.NUMBER, label: 'Number' },
  { value: FieldType.SELECT, label: 'Select' },
  { value: FieldType.MULTISELECT, label: 'Multi Select' },
  { value: FieldType.CHECKBOX, label: 'Checkbox' },
  { value: FieldType.RADIO, label: 'Radio' },
  { value: FieldType.DATE, label: 'Date' },
  { value: FieldType.TIME, label: 'Time' },
  { value: FieldType.DATETIME, label: 'Date Time' },
  { value: FieldType.FILE, label: 'File' },
  { value: FieldType.IMAGE, label: 'Image' },
  { value: FieldType.DISPLAY, label: 'Display' }
] as const;

export function FieldsStep() {
  const { formData, updateFormData, setIsValid } = useWizard();
  const { fields, addField, updateField, removeField, reorderFields, setFields } = useFieldStore();
  const [selectedType, setSelectedType] = useState<FieldType>(FieldType.TEXT);

  useEffect(() => {
    // Update form data when fields change
    updateFormData({
      customFields: fields.map(field => validateField(field))
    });

    // Validate fields
    const isValid = fields.every(field => {
      if (field.type === FieldType.DISPLAY) return true;
      if (field.required) {
        switch (field.type) {
          case FieldType.NUMBER:
            return (field as NumberField).defaultValue !== undefined;
          case FieldType.MULTISELECT:
            return (field as MultiSelectField).defaultValue !== undefined;
          case FieldType.CHECKBOX:
            return (field as CheckboxField).defaultValue !== undefined;
          case FieldType.SELECT:
          case FieldType.TEXT:
          case FieldType.TEXTAREA:
          case FieldType.EMAIL:
          case FieldType.PHONE:
          case FieldType.DATE:
          case FieldType.TIME:
          case FieldType.DATETIME:
          case FieldType.FILE:
          case FieldType.IMAGE:
            return (field as TextField | SelectField | DateField | FileField).defaultValue !== undefined;
          default:
            return true;
        }
      }
      return true;
    });
    setIsValid(isValid);
  }, [fields, updateFormData, setIsValid]);

  const handleAddField = () => {
    const field = FieldFactory.createField(selectedType);
    addField(field);
  };

  const handleFieldTypeChange = (id: string, type: FieldType) => {
    const field = fields.find(f => f.id === id);
    if (!field) return;

    // Create a new field with the new type while preserving common properties
    const newField = FieldFactory.createField(type, {
      id: field.id,
      label: field.label,
      description: field.description,
      placeholder: field.placeholder,
      required: field.required,
      order: field.order
    });

    updateField(id, newField);
  };

  const handleUpdateField = (id: string, updates: Partial<EventField>) => {
    const field = fields.find(f => f.id === id);
    if (!field) return;

    // Handle defaultValue type conversion
    if ('defaultValue' in updates) {
      const value = updates.defaultValue;
      let updatedField: EventField;

      switch (field.type) {
        case FieldType.NUMBER:
          updatedField = {
            ...field,
            defaultValue: typeof value === 'number' ? value : undefined
          } as NumberField;
          break;
        case FieldType.MULTISELECT:
          updatedField = {
            ...field,
            defaultValue: Array.isArray(value) ? value : undefined
          } as MultiSelectField;
          break;
        case FieldType.CHECKBOX:
          updatedField = {
            ...field,
            defaultValue: typeof value === 'boolean' ? value : undefined
          } as CheckboxField;
          break;
        case FieldType.SELECT:
        case FieldType.TEXT:
        case FieldType.TEXTAREA:
        case FieldType.EMAIL:
        case FieldType.PHONE:
        case FieldType.DATE:
        case FieldType.TIME:
        case FieldType.DATETIME:
        case FieldType.FILE:
        case FieldType.IMAGE:
          updatedField = {
            ...field,
            defaultValue: typeof value === 'string' ? value : undefined
          } as TextField | SelectField | DateField | FileField;
          break;
        case FieldType.DISPLAY:
          updatedField = field;
          break;
        default:
          updatedField = field;
      }

      updateField(id, validateField(updatedField));
      return;
    }

    // Handle other field updates
    const updatedField = validateField({ ...field, ...updates });
    updateField(id, updatedField);
  };

  const handleRemoveField = (id: string) => {
    removeField(id);
  };

  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return;
    reorderFields(result.source.index, result.destination.index);
  };

  const getFieldComponent = (field: EventField) => {
    switch (field.type) {
      case FieldType.TEXT:
      case FieldType.EMAIL:
      case FieldType.PHONE:
        return (
          <Input
            type={field.type}
            value={field.defaultValue || ''}
            onChange={(e) => handleUpdateField(field.id, { defaultValue: e.target.value })}
          />
        );
      case FieldType.NUMBER:
        return (
          <Input
            type="number"
            value={field.defaultValue || ''}
            onChange={(e) => handleUpdateField(field.id, { defaultValue: parseFloat(e.target.value) })}
          />
        );
      case FieldType.SELECT:
      case FieldType.MULTISELECT:
        return (
          <select
            value={field.defaultValue || ''}
            onChange={(e) => handleUpdateField(field.id, { defaultValue: e.target.value })}
          >
            {field.options.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );
      case FieldType.DATE:
      case FieldType.TIME:
      case FieldType.DATETIME:
        return (
          <Input
            type={field.type.toLowerCase()}
            value={field.defaultValue || ''}
            onChange={(e) => handleUpdateField(field.id, { defaultValue: e.target.value })}
          />
        );
      // ... rest of the cases ...
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-semibold">Custom Fields</h2>
        <div className="flex gap-2">
          <Select
            value={selectedType}
            onValueChange={(value) => setSelectedType(value as FieldType)}
          >
            {fieldTypes.map((type) => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </Select>
          <Button
            variant="outline"
            onClick={handleAddField}
          >
            Add Field
          </Button>
        </div>
      </div>

      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="fields">
          {(provided) => (
            <div
              {...provided.droppableProps}
              ref={provided.innerRef}
              className="space-y-4"
            >
              {fields.map((field, index) => (
                <Draggable
                  key={field.id}
                  draggableId={field.id}
                  index={index}
                >
                  {(provided) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      {...provided.dragHandleProps}
                      className="border rounded-lg p-4"
                    >
                      <div className="space-y-4">
                        <FormField>
                          <Label>Type</Label>
                          <Select
                            value={field.type}
                            onValueChange={(value) =>
                              handleFieldTypeChange(field.id, value as FieldType)
                            }
                          >
                            {fieldTypes.map((type) => (
                              <option key={type.value} value={type.value}>
                                {type.label}
                              </option>
                            ))}
                          </Select>
                        </FormField>

                        <FormField>
                          <Label>Label</Label>
                          <Input
                            value={field.label}
                            onChange={(e) =>
                              handleUpdateField(field.id, {
                                label: e.target.value,
                              })
                            }
                            placeholder="Field Label"
                          />
                        </FormField>

                        <FormField>
                          <Label>Description</Label>
                          <Input
                            value={field.description || ''}
                            onChange={(e) =>
                              handleUpdateField(field.id, {
                                description: e.target.value || null,
                              })
                            }
                            placeholder="Field Description"
                          />
                        </FormField>

                        <FormField>
                          <Label>Required</Label>
                          <input
                            type="checkbox"
                            checked={field.required}
                            onChange={(e) =>
                              handleUpdateField(field.id, {
                                required: e.target.checked,
                              })
                            }
                          />
                        </FormField>

                        {field.type !== FieldType.DISPLAY && (
                          <FormField>
                            <Label>Default Value</Label>
                            {getFieldComponent(field)}
                          </FormField>
                        )}

                        <Button
                          variant="destructive"
                          onClick={() => removeField(field.id)}
                        >
                          Remove Field
                        </Button>
                      </div>
                    </div>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>
    </div>
  );
}