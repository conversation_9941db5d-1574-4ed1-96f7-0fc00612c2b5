'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { useWizard, WizardStep } from '@/components/events/event-wizard/wizard-container';
import { EventType } from '@/types/event-types';

export function EventTypeStep({ eventTypes }: { eventTypes: EventType[] }) {
  const { formData, updateFormData, nextStep } = useWizard();
  const [selectedEventType, setSelectedEventType] = useState<string>(formData.eventTypeId || '');

  const handleSelectEventType = (typeId: string) => {
    setSelectedEventType(typeId);
    updateFormData({ eventTypeId: typeId });

    // Scroll to the Template Preview h3 element smoothly
    setTimeout(() => {
      const templatePreviewHeading = document.querySelector('[data-template-preview-heading]') as HTMLElement;
      if (templatePreviewHeading) {
        templatePreviewHeading.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 100);
  };

  // Enable next button when event type is selected
  useEffect(() => {
    // Find button using its ID instead of text content
    const nextButton = document.getElementById('wizard-next-button') as HTMLButtonElement;
    if (nextButton) {
      nextButton.disabled = !selectedEventType;
    }
  }, [selectedEventType]);

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-semibold mb-2">Choose Event Type</h2>
        <p className="text-[hsl(var(--muted-foreground))]">Select the type of event you want to create</p>
      </div>

      <RadioGroup
        value={selectedEventType}
        onValueChange={handleSelectEventType}
        className="grid grid-cols-1 md:grid-cols-2 gap-4"
      >
        {eventTypes.map((type) => (
          <div key={type.id}>
            <RadioGroupItem
              value={type.id}
              id={type.id}
              className="peer sr-only" />
            <Label
              htmlFor={type.id}
              className={`flex flex-col items-center justify-between rounded-md border-2 border-muted bg-[hsl(var(--popover))] p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary cursor-pointer event-type-card h-full ${selectedEventType === type.id ? 'selected' : ''}`}
            >
              <div className="mb-2 flex flex-col items-center">
                {type.icon && (
                  <div className={`mb-2 w-12 h-12 rounded-full flex items-center justify-center ${selectedEventType === type.id ? 'bg-primary text-white' : 'bg-primary/10 text-primary'}`}>
                    {/* Replace with appropriate icon component based on the type.icon value */}
                    <span className="text-2xl flex items-center justify-center w-full h-full">{type.icon}</span>
                  </div>
                )}
                <div className="font-semibold text-center">{type.name}</div>
              </div>
              <div className="text-sm text-[hsl(var(--muted-foreground))] text-center">{type.description}</div>
            </Label>
          </div>
        ))}
      </RadioGroup>

      {selectedEventType && (
        <div className="mt-6 bg-[hsl(var(--background))]">
          <h3 className="text-lg font-medium mb-2 scroll-mt-20" data-template-preview-heading>Template Preview</h3>
          <div className="border border-[hsl(var(--border))] rounded-md p-4 bg-[hsl(var(--muted))]">
            <div className="text-[hsl(var(--foreground))]">
              {eventTypes.find(t => t.id === selectedEventType)?.description}
            </div>
            <ul className="mt-2 text-sm text-[hsl(var(--muted-foreground))]">
              {eventTypes.find(t => t.id === selectedEventType)?.baseFields &&
                Object.entries(eventTypes.find(t => t.id === selectedEventType)?.baseFields || {})
                  .filter(([_, value]) => value)
                  .map(([key]) => (
                    <li key={key} className="flex items-center gap-2">
                      <span className="text-[hsl(var(--success))]">✓</span>
                      {key.replace(/([A-Z])/g, ' $1')
                        .replace(/^./, str => str.toUpperCase())
                        .replace(/([a-z])([A-Z])/g, '$1 $2')}
                    </li>
                  ))
              }
            </ul>
          </div>
        </div>
      )}
    </div>
  );
}