'use client';

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { PersonalInfo } from "@/components/profile/personal-info";
import { ContactsList } from "@/components/profile/contacts-list";
import { PrivacySettings } from "@/components/profile/privacy-settings";
import { ActivityHistory } from "@/components/profile/activity-history";
import { DataExport } from "@/components/profile/data-export";
import { ProfileCompletion } from "@/components/profile/profile-completion";
import { Button } from "@/components/ui/button";
import { Pencil, ChevronDown } from "lucide-react";
import { createPortal } from "react-dom";
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/auth'
import { Preferences } from '@/components/profile/preferences'
import { Security } from '@/components/profile/security'
import { Notifications } from '@/components/profile/notifications'

interface UserData {
  id: string;
  first_name: string;
  last_name?: string;
  email: string;
  role: string;
  gender?: string;
  username?: string;
}

const navigationItems = [
  {
    href: "/dashboard/profile",
    label: "Profile",
    iconName: "UserCircle",
  },
  {
    href: "/dashboard",
    label: "Overview",
    iconName: "LayoutDashboard",
  },
  {
    href: "/dashboard/events",
    label: "Events",
    iconName: "Calendar",
  },
  {
    href: "/dashboard/organizations",
    label: "Organizations",
    iconName: "Users",
  },
  {
    href: "/dashboard/tickets",
    label: "Tickets",
    iconName: "Ticket",
  },
  {
    href: "/dashboard/payments",
    label: "Payments",
    iconName: "CreditCard",
  },
];

export function ProfileClient() {
  const { user } = useAuth()
  const router = useRouter()

  if (!user) {
    router.push('/signin')
    return null
  }

  const [activeTab, setActiveTab] = useState("info");
  const [isEditing, setIsEditing] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [portalContainer, setPortalContainer] = useState<HTMLElement | null>(null);

  useEffect(() => {
    setPortalContainer(document.body);
  }, []);

  // Function to toggle edit mode state that can be passed to the PersonalInfo component
  const toggleEditMode = () => {
    setIsEditing(!isEditing);
  };

  // Tab options mapping for dropdown
  const tabOptions = {
    "info": "Personal Info",
    "contacts": "Contacts",
    "privacy": "Privacy",
    "activity": "Activity",
    "export": "Data Export"
  };

  // Close dropdown when clicked outside
  useEffect(() => {
    if (!mobileMenuOpen) return;

    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (!target.closest('.mobile-tabs-dropdown-button') &&
        !target.closest('.mobile-tabs-dropdown-menu')) {
        setMobileMenuOpen(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [mobileMenuOpen]);

  return (
    <div className="container mx-auto py-10">
      <div className="w-full py-4 space-y-4 relative">
        <Tabs defaultValue="personal" className="space-y-4">
          <TabsList>
            <TabsTrigger value="personal">Personal Info</TabsTrigger>
            <TabsTrigger value="preferences">Preferences</TabsTrigger>
            <TabsTrigger value="security">Security</TabsTrigger>
            <TabsTrigger value="notifications">Notifications</TabsTrigger>
          </TabsList>

          <TabsContent value="personal">
            <Card>
              <CardHeader>
                <CardTitle>Personal Information</CardTitle>
              </CardHeader>
              <CardContent>
                <PersonalInfo user={user} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="preferences">
            <Card>
              <CardHeader>
                <CardTitle>Preferences</CardTitle>
              </CardHeader>
              <CardContent>
                <Preferences user={user} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="security">
            <Card>
              <CardHeader>
                <CardTitle>Security</CardTitle>
              </CardHeader>
              <CardContent>
                <Security user={user} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="notifications">
            <Card>
              <CardHeader>
                <CardTitle>Notifications</CardTitle>
              </CardHeader>
              <CardContent>
                <Notifications user={user} />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}