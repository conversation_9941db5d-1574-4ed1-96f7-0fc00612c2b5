import { createClient } from '@/lib/supabase/pages-client'
import { NextResponse } from "next/server"

export async function GET() {
  const supabase = await createClient()

  const { data, error } = await (supabase as any)
    .from("categories")
    .select("*")
    .order("name")

  if (error) {
    return NextResponse.json({ message: error.message }, { status: 500 })
  }

  return NextResponse.json(data)
}

export async function POST(request: Request) {
  const supabase = await createClient()
  const body = await request.json()

  const { error } = await (supabase as any)
    .from("categories")
    .insert([{ name: body.name, description: body.description }])

  if (error) {
    return NextResponse.json({ message: error.message }, { status: 500 })
  }

  return NextResponse.json({ message: "Category created successfully" })
}

export async function PUT(request: Request) {
  const supabase = await createClient()
  const body = await request.json()

  const { error } = await (supabase as any)
    .from("categories")
    .update({ name: body.name, description: body.description })
    .eq("id", body.id)

  if (error) {
    return NextResponse.json({ message: error.message }, { status: 500 })
  }

  return NextResponse.json({ message: "Category updated successfully" })
}

export async function DELETE(request: Request) {
  const supabase = await createClient()
  const { searchParams } = new URL(request.url)
  const id = searchParams.get("id")

  if (!id) {
    return NextResponse.json({ error: "Category ID is required" }, { status: 400 })
  }

  const { error } = await (supabase as any)
    .from("categories")
    .delete()
    .eq("id", id)

  if (error) {
    return NextResponse.json({ message: error.message }, { status: 500 })
  }

  return NextResponse.json({ message: "Category deleted successfully" })
}