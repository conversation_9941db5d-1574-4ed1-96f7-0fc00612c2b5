'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Search, Plus, Pencil, Trash2, ChevronRight, Loader2, AlertCircle } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { useForm, Controller, Control } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { getSavedContacts, createContact, updateContact, deleteContact } from '@/app/actions/contacts';
import {
  Contact,
  ContactSchema,
  ContactDatabase,
  ContactResponse,
  ContactError,
  ContactRelationship,
  EmergencyContactRelationship,
  transformDatabaseContact,
  transformContactToDatabase
} from '@/types/contacts/schema';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import Select from 'react-select';
import { getCountries, getStates, type Country, type State } from '@/data/countries';
import { logger } from '@/lib/logger';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select as UISelect,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from '@/components/ui/use-toast';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

interface FormData {
  firstName: string;
  lastName: string | null;
  fullName: string | null;
  relationship: ContactRelationship;
  email: string | null;
  phone: string | null;
  dateOfBirth: string | null;
  gender: string | null;
  tshirtSize: string | null;
  address: string | null;
  city: string | null;
  state: string | null;
  country: string | null;
  postcode: string | null;
  notes: string | null;
  isEmergencyContact: boolean | null;
  emergencyContactName: string | null;
  emergencyContactNo: string | null;
  emergencyContactRelationship: EmergencyContactRelationship | null;
  userId: string;
  updatedAt: string | null;
}

interface SelectOption {
  value: string;
  label: string;
  icon?: React.ReactNode;
}

interface ControlledSelectProps {
  name: string;
  control: Control<FormData>;
  options: SelectOption[];
  placeholder: string;
  isDisabled?: boolean;
  isLoading?: boolean;
  onChange?: (value: string | undefined) => void;
}

const ControlledSelect: React.FC<ControlledSelectProps> = ({
  name,
  control,
  options,
  placeholder,
  isDisabled,
  isLoading,
  onChange
}) => (
  <Controller
    name={name as keyof FormData}
    control={control}
    render={({ field }) => (
      <Select
        {...field}
        className="react-select"
        classNamePrefix="react-select"
        options={options}
        placeholder={placeholder}
        isClearable
        isDisabled={isDisabled}
        isLoading={isLoading}
        value={options.find(x => x.value === field.value) || null}
        onChange={(option) => {
          field.onChange(option?.value);
          onChange?.(option?.value);
        }}
        formatOptionLabel={(option) => (
          <div className="flex items-center">
            {option.icon && <span className="mr-2">{option.icon}</span>}
            <span>{option.label}</span>
          </div>
        )}
      />
    )}
  />
);

interface ContactsListProps {
  className?: string;
}

interface FormFieldProps {
  name: keyof FormData;
  label: string;
  placeholder?: string;
  description?: string;
  isRequired?: boolean;
  type?: string;
  control: Control<FormData>;
}

const LoadingSkeleton = () => (
  <div className="space-y-4">
    {[1, 2, 3].map((item) => (
      <Card key={item} className="animate-pulse">
        <CardContent className="p-6">
          <div className="flex justify-between items-center">
            <div className="space-y-2">
              <div className="h-5 bg-muted rounded w-32" />
              <div className="h-4 bg-muted rounded w-24" />
              <div className="h-4 bg-muted rounded w-40" />
            </div>
            <div className="flex gap-2">
              <div className="h-8 w-8 bg-muted rounded" />
              <div className="h-8 w-8 bg-muted rounded" />
              <div className="h-8 w-8 bg-muted rounded" />
            </div>
          </div>
        </CardContent>
      </Card>
    ))}
  </div>
);

// Add ValidationMessage component
interface ValidationMessageProps {
  message: string;
  type: 'error' | 'warning' | 'info';
}

const ValidationMessage: React.FC<ValidationMessageProps> = ({ message, type }) => {
  const styles = {
    error: 'text-destructive',
    warning: 'text-warning',
    info: 'text-muted-foreground'
  };

  return (
    <div className={`flex items-center gap-2 text-sm ${styles[type]}`}>
      {type === 'error' && <AlertCircle className="h-4 w-4" />}
      <span>{message}</span>
    </div>
  );
};

// Add FormErrorAlert component
interface FormErrorAlertProps {
  title: string;
  description: string;
  onRetry?: () => void;
}

const FormErrorAlert: React.FC<FormErrorAlertProps> = ({ title, description, onRetry }) => (
  <Alert variant="destructive" className="mb-4">
    <AlertCircle className="h-4 w-4" />
    <AlertTitle>{title}</AlertTitle>
    <AlertDescription className="flex items-center justify-between">
      <span>{description}</span>
      {onRetry && (
        <Button variant="outline" size="sm" onClick={onRetry}>
          Try Again
        </Button>
      )}
    </AlertDescription>
  </Alert>
);

// Update FormTextField to fix type issues
const FormTextField: React.FC<FormFieldProps> = ({
  name,
  label,
  placeholder,
  description,
  isRequired,
  type = "text",
  control,
}) => (
  <FormField
    control={control}
    name={name}
    render={({ field, fieldState: { error, isDirty, isTouched } }) => (
      <FormItem>
        <FormLabel>
          {label}
          {isRequired && <span className="text-destructive ml-1">*</span>}
        </FormLabel>
        <FormControl>
          <div className="space-y-2">
            <Input
              type={type}
              placeholder={placeholder}
              {...field}
              value={field.value?.toString() || ''}
              className={error ? 'border-destructive' : ''}
            />
            {error?.message && <ValidationMessage message={error.message} type="error" />}
            {!error && isDirty && isTouched && (
              <ValidationMessage message="Looks good!" type="info" />
            )}
          </div>
        </FormControl>
        {description && <FormDescription>{description}</FormDescription>}
      </FormItem>
    )}
  />
);

const GenderRadioGroup: React.FC<{ control: Control<FormData> }> = ({ control }) => (
  <FormField
    control={control}
    name="gender"
    render={({ field }) => (
      <FormItem className="space-y-3">
        <FormLabel>Gender</FormLabel>
        <FormControl>
          <RadioGroup
            onValueChange={field.onChange}
            value={field.value || ''}
            className="flex flex-col space-y-1"
          >
            <FormItem className="flex items-center space-x-3 space-y-0">
              <FormControl>
                <RadioGroupItem value="male" />
              </FormControl>
              <FormLabel className="font-normal">Male</FormLabel>
            </FormItem>
            <FormItem className="flex items-center space-x-3 space-y-0">
              <FormControl>
                <RadioGroupItem value="female" />
              </FormControl>
              <FormLabel className="font-normal">Female</FormLabel>
            </FormItem>
            <FormItem className="flex items-center space-x-3 space-y-0">
              <FormControl>
                <RadioGroupItem value="other" />
              </FormControl>
              <FormLabel className="font-normal">Other</FormLabel>
            </FormItem>
          </RadioGroup>
        </FormControl>
        <FormMessage />
      </FormItem>
    )}
  />
);

const TShirtSizeSelect: React.FC<{ control: Control<FormData> }> = ({ control }) => (
  <FormField
    control={control}
    name="tshirtSize"
    render={({ field }) => (
      <FormItem>
        <FormLabel>T-Shirt Size</FormLabel>
        <FormControl>
          <UISelect
            onValueChange={field.onChange}
            value={field.value || ''}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select size" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="XS">XS</SelectItem>
              <SelectItem value="S">S</SelectItem>
              <SelectItem value="M">M</SelectItem>
              <SelectItem value="L">L</SelectItem>
              <SelectItem value="XL">XL</SelectItem>
              <SelectItem value="2XL">2XL</SelectItem>
              <SelectItem value="3XL">3XL</SelectItem>
            </SelectContent>
          </UISelect>
        </FormControl>
        <FormMessage />
      </FormItem>
    )}
  />
);

interface CountryStateSelectProps {
  control: Control<FormData>;
  countryOptions: SelectOption[];
  stateOptions: SelectOption[];
  isLoadingStates: boolean;
  onCountryChange: (countryCode: string | undefined) => void;
}

const CountryStateSelect: React.FC<CountryStateSelectProps> = ({
  control,
  countryOptions,
  stateOptions,
  isLoadingStates,
  onCountryChange
}) => {
  return (
    <div className="space-y-4">
      <FormField
        control={control}
        name="country"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Country</FormLabel>
            <FormControl>
              <ControlledSelect
                name="country"
                control={control}
                options={countryOptions}
                placeholder="Select country"
                onChange={onCountryChange}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={control}
        name="state"
        render={({ field }) => (
          <FormItem>
            <FormLabel>State/Province</FormLabel>
            <FormControl>
              <ControlledSelect
                name="state"
                control={control}
                options={stateOptions}
                placeholder="Select state/province"
                isDisabled={stateOptions.length === 0}
                isLoading={isLoadingStates}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};

// Update the DialogFooter component
interface DialogFooterProps {
  isSubmitting: boolean;
  selectedContact: Contact | null;
}

const ContactDialogFooter: React.FC<DialogFooterProps> = ({ isSubmitting, selectedContact }) => (
  <DialogFooter>
    <Button type="submit" disabled={isSubmitting}>
      {isSubmitting ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          {selectedContact ? "Updating..." : "Creating..."}
        </>
      ) : (
        selectedContact ? "Update Contact" : "Add Contact"
      )}
    </Button>
  </DialogFooter>
);

// Update the DeleteConfirmationDialog component
interface DeleteConfirmationDialogProps {
  confirmDelete: string | null;
  deletingId: string | null;
  onClose: () => void;
  onConfirm: (id: string) => void;
}

const DeleteConfirmationDialog: React.FC<DeleteConfirmationDialogProps> = ({
  confirmDelete,
  deletingId,
  onClose,
  onConfirm
}) => (
  <Dialog
    open={!!confirmDelete}
    onOpenChange={(open) => !open && onClose()}
  >
    <DialogContent className="sm:max-w-[425px]">
      <DialogHeader>
        <DialogTitle>Delete Contact</DialogTitle>
        <DialogDescription>
          Are you sure you want to delete this contact? This action cannot be undone.
        </DialogDescription>
      </DialogHeader>
      <DialogFooter>
        <Button
          variant="outline"
          onClick={onClose}
          disabled={!!deletingId}
        >
          Cancel
        </Button>
        <Button
          variant="destructive"
          onClick={() => {
            if (confirmDelete) {
              onConfirm(confirmDelete);
            }
          }}
          disabled={!!deletingId}
        >
          {deletingId === confirmDelete ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Deleting...
            </>
          ) : (
            "Delete"
          )}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
);

// Fix the search input type safety
const SearchInput: React.FC<{ value: string; onChange: (value: string) => void }> = ({ value, onChange }) => (
  <div className="relative">
    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
    <Input
      placeholder="Search contacts..."
      value={value}
      onChange={(e) => onChange(e.target.value)}
      className="pl-8"
      type="search"
      aria-label="Search contacts"
    />
  </div>
);

// Add accessibility improvements to the contact card
interface ContactCardProps {
  contact: Contact;
  onEdit: () => void;
  onDelete: () => void;
}

const ContactCard: React.FC<ContactCardProps> = ({ contact, onEdit, onDelete }) => (
  <Card>
    <CardContent className="p-6">
      <div className="flex justify-between items-center">
        <div className="space-y-1">
          <h3 className="font-medium leading-none">
            {contact.firstName} {contact.lastName}
          </h3>
          {contact.relationship && (
            <p className="text-sm text-muted-foreground">
              {contact.relationship}
            </p>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={onEdit}
            aria-label={`Edit ${contact.firstName}'s details`}
          >
            <Pencil className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={onDelete}
            aria-label={`Delete ${contact.firstName} from contacts`}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
      <div className="mt-4 space-y-2">
        {contact.email && (
          <p className="text-sm">
            <span className="font-medium">Email:</span> {contact.email}
          </p>
        )}
        {contact.phone && (
          <p className="text-sm">
            <span className="font-medium">Phone:</span> {contact.phone}
          </p>
        )}
      </div>
    </CardContent>
  </Card>
);

// Add accessibility improvements to the dialog
const ContactDialog: React.FC<{
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  children: React.ReactNode;
}> = ({ open, onOpenChange, title, children }) => (
  <Dialog open={open} onOpenChange={onOpenChange}>
    <DialogContent className="max-w-2xl">
      <DialogHeader>
        <DialogTitle>{title}</DialogTitle>
      </DialogHeader>
      {children}
    </DialogContent>
  </Dialog>
);

// Update the main component with proper type handling
export default function ContactsList() {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [confirmDelete, setConfirmDelete] = useState<string | null>(null);
  const [countryOptions, setCountryOptions] = useState<SelectOption[]>([]);
  const [stateOptions, setStateOptions] = useState<SelectOption[]>([]);
  const [isLoadingCountries, setIsLoadingCountries] = useState(true);
  const [isLoadingStates, setIsLoadingStates] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [formError, setFormError] = useState<Error | null>(null);

  const form = useForm<FormData>({
    resolver: zodResolver(ContactSchema),
    defaultValues: {
      firstName: '',
      lastName: null,
      fullName: null,
      relationship: 'FAMILY' as ContactRelationship,
      email: null,
      phone: null,
      dateOfBirth: null,
      gender: null,
      tshirtSize: null,
      address: null,
      city: null,
      state: null,
      country: null,
      postcode: null,
      notes: null,
      isEmergencyContact: null,
      emergencyContactName: null,
      emergencyContactNo: null,
      emergencyContactRelationship: null,
      userId: '',
      updatedAt: null
    },
  });

  // Fetch contacts on component mount
  useEffect(() => {
    const fetchContacts = async () => {
      logger.info("ContactsList: Starting to fetch contacts");
      setLoading(true);
      try {
        logger.info("ContactsList: Calling getSavedContacts server action");
        const response = await getSavedContacts();
        if (response.error) {
          throw response.error;
        }
        if (response.data) {
          setContacts(response.data);
        }
      } catch (error) {
        const err = error as ContactError;
        logger.error("ContactsList: Error fetching contacts", err);
        toast({
          title: "Error",
          description: err.message || "Failed to load contacts. Please try again.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchContacts();
  }, []);

  // Fetch countries on mount
  useEffect(() => {
    const fetchCountryOptions = async () => {
      try {
        const countries = await getCountries();
        const options = countries.map((country: Country) => ({
          value: country.code,
          label: country.name,
          icon: country.flag
        }));
        setCountryOptions(options);
      } catch (error) {
        console.error('Error fetching country options:', error);
        toast({
          title: "Error",
          description: "Failed to load country options. Please try again.",
          variant: "destructive",
        });
      }
    };

    fetchCountryOptions();
  }, []);

  const handleCountryChange = async (countryCode: string | undefined) => {
    setIsLoadingStates(true);
    form.setValue('state', '');
    setStateOptions([]);

    if (countryCode) {
      try {
        const states = await getStates(countryCode);
        const options = states.map((state: State) => ({
          value: state.code,
          label: state.name
        }));
        setStateOptions(options);
      } catch (error) {
        console.error('Error fetching state options:', error);
        toast({
          title: "Error",
          description: "Failed to load state options. Please try again.",
          variant: "destructive",
        });
      }
    }
    setIsLoadingStates(false);
  };

  // Update the filtered contacts logic with proper null handling
  const filteredContacts = useMemo(() => {
    if (!searchQuery) return contacts;
    const query = searchQuery.toLowerCase();
    return contacts.filter((contact) => {
      const fullName = `${contact.firstName} ${contact.lastName || ''}`.toLowerCase();
      const email = (contact.email || '').toLowerCase();
      const phone = (contact.phone || '').toLowerCase();
      return (
        fullName.includes(query) ||
        email.includes(query) ||
        phone.includes(query)
      );
    });
  }, [contacts, searchQuery]);

  // Open dialog for creating/editing a contact
  const openContactDialog = (contact: Contact | null = null) => {
    if (contact) {
      form.reset(contact);
      setSelectedContact(contact);
    } else {
      form.reset();
      setSelectedContact(null);
    }
    setDialogOpen(true);
  };

  // Handle form submission
  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true);
    setFormError(null);

    try {
      const contactData = {
        ...data,
        userId: '', // This will be set by the server
      };

      if (selectedContact?.id) {
        const response = await updateContact(selectedContact.id, transformContactToDatabase(contactData));
        if (response.error) {
          throw new Error(response.error.message || 'Failed to update contact');
        }
        updateContacts(response);
        toast({
          title: "Success",
          description: "Contact updated successfully",
        });
      } else {
        const response = await createContact(transformContactToDatabase(contactData));
        if (response.error) {
          throw new Error(response.error.message || 'Failed to create contact');
        }
        updateContacts(response);
        toast({
          title: "Success",
          description: "Contact created successfully",
        });
      }
    } catch (error) {
      console.error('Error submitting contact:', error);
      setFormError(error instanceof Error ? error : new Error('An unexpected error occurred'));
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save contact",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle contact deletion
  const handleDeleteContact = async (id: string) => {
    setDeletingId(id);
    try {
      const result = await deleteContact(id);
      if (result.error) {
        throw new Error(result.error.message || 'Failed to delete contact');
      }

      setContacts(prev => prev.filter(contact => contact.id !== id));
      setConfirmDelete(null);

      toast({
        title: "Success",
        description: "Contact deleted successfully",
      });
    } catch (error) {
      console.error('Error deleting contact:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete contact",
        variant: "destructive",
      });
    } finally {
      setDeletingId(null);
    }
  };

  const openDeleteConfirmation = (contactId: string | undefined) => {
    if (contactId) {
      setConfirmDelete(contactId);
    }
  };

  // Update the contacts state setter with proper type safety
  const updateContacts = (response: ContactResponse<Contact>) => {
    if (response.data) {
      setContacts((prev: Contact[]) => {
        if (selectedContact?.id) {
          // When updating an existing contact, ensure we return a Contact[]
          return prev.map(c =>
            c.id === selectedContact.id ? response.data as Contact : c
          );
        }
        // When adding a new contact, ensure we return a Contact[]
        return [...prev, response.data as Contact];
      });

      // Close dialog and reset form
      setDialogOpen(false);
      form.reset();
      setSelectedContact(null);
    }
  };

  if (loading) {
    return <LoadingSkeleton />;
  }

  return (
    <div role="region" aria-label="Contacts Management">
      <div className="flex items-center justify-between space-x-4 mb-4">
        <SearchInput
          value={searchQuery}
          onChange={setSearchQuery}
        />
        <Button
          onClick={() => {
            setSelectedContact(null);
            setDialogOpen(true);
          }}
          aria-label="Add new contact"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Contact
        </Button>
      </div>

      {loading ? (
        <LoadingSkeleton />
      ) : filteredContacts.length === 0 ? (
        <div className="text-center py-12 border rounded-lg">
          <div className="text-4xl mb-4">👥</div>
          <h3 className="text-lg font-medium mb-2">No contacts found</h3>
          <p className="text-muted-foreground mb-6">
            {searchQuery ? "No contacts match your search" : "You haven't added any contacts yet"}
          </p>
          {searchQuery ? (
            <Button variant="outline" onClick={() => setSearchQuery('')}>
              Clear search
            </Button>
          ) : (
            <Button onClick={() => openContactDialog()}>
              <Plus className="mr-2 h-4 w-4" />
              Add your first contact
            </Button>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          {filteredContacts.map((contact) => (
            <ContactCard
              key={contact.id}
              contact={contact}
              onEdit={() => {
                setSelectedContact(contact);
                setDialogOpen(true);
              }}
              onDelete={() => contact.id && setConfirmDelete(contact.id)}
            />
          ))}
        </div>
      )}

      <ContactDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        title={selectedContact ? "Edit Contact" : "Add Contact"}
      >
        {formError && (
          <FormErrorAlert
            title="Error saving contact"
            description={formError.message}
          />
        )}
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <Tabs defaultValue="basic">
            <TabsList className="grid grid-cols-3 mb-4">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="address">Address</TabsTrigger>
              <TabsTrigger value="emergency">Emergency Contact</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              <div className="grid gap-4 grid-cols-1 sm:grid-cols-2">
                <FormTextField
                  name="firstName"
                  label="First Name"
                  placeholder="First name"
                  isRequired
                  control={form.control}
                />
                <FormTextField
                  name="lastName"
                  label="Last Name"
                  placeholder="Last name"
                  control={form.control}
                />
              </div>

              <FormField
                control={form.control}
                name="relationship"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Relationship</FormLabel>
                    <UISelect
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select relationship" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Family">Family</SelectItem>
                        <SelectItem value="Friend">Friend</SelectItem>
                        <SelectItem value="Spouse">Spouse</SelectItem>
                        <SelectItem value="Colleague">Colleague</SelectItem>
                        <SelectItem value="Other">Other</SelectItem>
                      </SelectContent>
                    </UISelect>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid gap-4 grid-cols-1 sm:grid-cols-2">
                <FormTextField
                  name="email"
                  label="Email"
                  placeholder="Email address"
                  type="email"
                  control={form.control}
                />
                <FormTextField
                  name="phone"
                  label="Phone"
                  placeholder="Phone number"
                  control={form.control}
                />
              </div>

              <div className="grid gap-4 grid-cols-1 sm:grid-cols-2">
                <FormTextField
                  name="dateOfBirth"
                  label="Date of Birth"
                  placeholder="Date of birth"
                  type="date"
                  control={form.control}
                />
                <GenderRadioGroup control={form.control} />
              </div>

              <TShirtSizeSelect control={form.control} />
            </TabsContent>

            <TabsContent value="address" className="space-y-4">
              <FormTextField
                name="address"
                label="Street Address"
                placeholder="Street address"
                control={form.control}
              />

              <div className="grid gap-4 grid-cols-1 sm:grid-cols-2">
                <FormTextField
                  name="city"
                  label="City"
                  placeholder="City"
                  control={form.control}
                />
                <FormTextField
                  name="state"
                  label="State/Province"
                  placeholder="Select state/province..."
                  control={form.control}
                />
              </div>

              <div className="grid gap-4 grid-cols-1 sm:grid-cols-2">
                <CountryStateSelect
                  control={form.control}
                  countryOptions={countryOptions}
                  stateOptions={stateOptions}
                  isLoadingStates={isLoadingStates}
                  onCountryChange={handleCountryChange}
                />
                <FormTextField
                  name="postcode"
                  label="Postal/ZIP Code"
                  placeholder="Postal/ZIP code"
                  control={form.control}
                />
              </div>
            </TabsContent>

            <TabsContent value="emergency" className="space-y-4">
              <FormTextField
                name="emergencyContactName"
                label="Emergency Contact Name"
                placeholder="Full name"
                control={form.control}
              />

              <FormTextField
                name="emergencyContactNo"
                label="Emergency Contact Number"
                placeholder="Phone number"
                control={form.control}
              />

              <FormField
                control={form.control}
                name="emergencyContactRelationship"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Relationship to Emergency Contact</FormLabel>
                    <FormControl>
                      <UISelect
                        onValueChange={field.onChange}
                        defaultValue={field.value || ''}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select relationship" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Parent">Parent</SelectItem>
                          <SelectItem value="Spouse">Spouse</SelectItem>
                          <SelectItem value="Sibling">Sibling</SelectItem>
                          <SelectItem value="Friend">Friend</SelectItem>
                          <SelectItem value="Other">Other</SelectItem>
                        </SelectContent>
                      </UISelect>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </TabsContent>
          </Tabs>

          <ContactDialogFooter
            isSubmitting={isSubmitting}
            selectedContact={selectedContact}
          />
        </form>
      </ContactDialog>

      <DeleteConfirmationDialog
        confirmDelete={confirmDelete}
        deletingId={deletingId}
        onClose={() => setConfirmDelete(null)}
        onConfirm={handleDeleteContact}
      />
    </div>
  );
}
