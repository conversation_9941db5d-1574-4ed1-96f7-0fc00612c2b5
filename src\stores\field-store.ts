/**
 * Field store for managing form fields
 * @module stores/field-store
 */

import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import type { EventField } from '@/types/event/field-types';
import { eventFieldSchema } from '@/types/event/field-types';

interface FieldState {
    fields: EventField[];
    validationErrors: Record<string, string[]>;
    addField: (field: EventField) => void;
    updateField: (id: string, field: EventField) => void;
    removeField: (id: string) => void;
    reorderFields: (startIndex: number, endIndex: number) => void;
    setValidationError: (fieldId: string, errors: string[]) => void;
    clearValidationError: (fieldId: string) => void;
    clearAllValidationErrors: () => void;
}

export const useFieldStore = create<FieldState>()(
    immer((set) => ({
        fields: [],
        validationErrors: {},

        addField: (field) => {
            // Validate field before adding
            const validatedField = eventFieldSchema.parse(field);
            set((state) => {
                state.fields.push(validatedField);
            });
        },

        updateField: (id, field) => {
            // Validate field before updating
            const validatedField = eventFieldSchema.parse(field);
            set((state) => {
                const index = state.fields.findIndex((f) => f.id === id);
                if (index !== -1) {
                    state.fields[index] = validatedField;
                }
            });
        },

        removeField: (id) => {
            set((state) => {
                state.fields = state.fields.filter((f) => f.id !== id);
                delete state.validationErrors[id];
            });
        },

        reorderFields: (startIndex, endIndex) => {
            set((state) => {
                const result = Array.from(state.fields);
                const [removed] = result.splice(startIndex, 1);
                result.splice(endIndex, 0, removed);

                // Update order property
                const reordered = result.map((field, index) => ({
                    ...field,
                    order: index
                }));

                state.fields = reordered;
            });
        },

        setValidationError: (fieldId, errors) => {
            set((state) => {
                state.validationErrors[fieldId] = errors;
            });
        },

        clearValidationError: (fieldId) => {
            set((state) => {
                delete state.validationErrors[fieldId];
            });
        },

        clearAllValidationErrors: () => {
            set((state) => {
                state.validationErrors = {};
            });
        },
    }))
); 