import React from 'react';
import Link from 'next/link';
import { redirect } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { EventRepository, Event } from '@/repositories/event-repository';
import { Badge } from '@/components/ui/badge';
import { Calendar, Edit, Eye, PlusCircle } from 'lucide-react';
import { createClient } from '@/lib/supabase/server';
import { formatDistance } from 'date-fns';
import { EventCompletionIndicator } from '@/components/events/dashboard/event-completion-indicator';
import { EventActions } from '@/components/events/dashboard/event-actions';
import { logger } from '@/lib/logger';

export default async function EventsPage() {
  // Get the Supabase client
  const supabase = await createClient();

  // Get the authenticated user - middleware already ensures we have a valid user
  const { data: { user: authUser } } = await supabase.auth.getUser();

  // This should never happen since middleware protects this route
  if (!authUser) {
    console.error("User not found in events page despite middleware protection");
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-2">Session Error</h1>
        <p>Please try <a href="/sign-in?reset_auth=true" className="text-primary underline">signing in again</a>.</p>
      </div>
    );
  }

  if (process.env.NODE_ENV === 'development') {
    logger.debug(`[DEBUG] EventsPage - Authenticated with Supabase userId: ${authUser.id}`);
  }

  // Get the internal user ID from the users table
  const { data: userData, error: userIdError } = await supabase
    .from('users')
    .select('id')
    .eq('auth_user_id', authUser.id)
    .single();

  // Handle missing user data without redirecting
  if (userIdError || !userData) {
    console.error("Error fetching user ID:", userIdError);
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-2">User Data Error</h1>
        <p>Unable to fetch your user data. Please try again later.</p>
      </div>
    );
  }

  const userId = userData.id;
  if (process.env.NODE_ENV === 'development') {
    logger.debug(`[DEBUG] EventsPage - Internal user ID: ${userId}`);
  }

  // Fetch user's events
  const eventRepository = new EventRepository();
  let events: Event[] = [];
  try {
    if (process.env.NODE_ENV === 'development') {
      logger.debug(`[DEBUG] EventsPage - Fetching events for userId: ${userId}`);
    }
    events = await eventRepository.getEventsByOrganizerId(userId);
    if (process.env.NODE_ENV === 'development') {
      logger.debug(`[DEBUG] EventsPage - Total events fetched: ${events.length}`);

      // Log all event IDs and their statuses for debugging
      if (events.length > 0) {
        logger.debug(`[DEBUG] EventsPage - Events details:`,
          events.map(e => ({ id: e.id, title: e.title, status: e.status, createdAt: e.createdAt }))
        );
      } else {
        logger.debug(`[DEBUG] EventsPage - No events found for user`);
      }
    }
  } catch (error) {
    console.error("[DEBUG] EventsPage - Error fetching events:", error);
  }

  // Separate events by status
  const draftEvents = events.filter(event => event.status === 'draft');
  const publishedEvents = events.filter(event => event.status === 'published');

  if (process.env.NODE_ENV === 'development') {
    logger.debug(`[DEBUG] EventsPage - Draft events: ${draftEvents.length}, Published events: ${publishedEvents.length}`);
    if (draftEvents.length > 0) {
      logger.debug(`[DEBUG] EventsPage - Draft events details:`,
        draftEvents.map(e => ({ id: e.id, title: e.title, createdAt: e.createdAt }))
      );
    }
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold mb-2">Events</h1>
          <p className="text-[hsl(var(--muted-foreground))]">Manage and explore events</p>
        </div>
        <Link href="/dashboard/events/new">
          <Button className="px-4 py-2 bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))] rounded-md hover:bg-[hsl(var(--primary-hover))] transition">
            <PlusCircle className="w-4 h-4 mr-2" />
            Create Event
          </Button>
        </Link>
      </div>

      {/* Published Events Section */}
      <div className="mb-8">
        <div className="bg-[hsl(var(--card))] text-[hsl(var(--card-foreground))] rounded-lg shadow p-6 border border-[hsl(var(--border))]">
          <h2 className="text-xl font-semibold mb-4">Published Events</h2>

          {publishedEvents.length === 0 ? (
            <div className="border border-[hsl(var(--border))] rounded-lg overflow-hidden">
              <div className="p-8 text-center text-[hsl(var(--muted-foreground))]">
                <p>No published events found.</p>
              </div>
            </div>
          ) : (
            <div className="border border-[hsl(var(--border))] rounded-lg overflow-hidden">
              <div className="divide-y divide-[hsl(var(--border))]">
                {publishedEvents.map((event) => (
                  <div key={event.id} className="p-4 hover:bg-[hsl(var(--primary-50))] dark:hover:bg-[hsl(var(--primary-subtle))] transition-colors flex justify-between items-center">
                    <div>
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">{event.title}</h3>
                        <Badge variant="outline" className="bg-green-50 dark:bg-green-950/30 text-green-600 dark:text-green-400 border-green-200 dark:border-green-800/50">
                          Published
                        </Badge>
                      </div>
                      <p className="text-sm text-[hsl(var(--muted-foreground))] mt-1">
                        {event.startDate ? new Date(event.startDate).toLocaleDateString() : 'No start date'} - {event.endDate ? new Date(event.endDate).toLocaleDateString() : 'No end date'}
                      </p>
                    </div>
                    <div className="flex gap-2 items-center">
                      <div className="flex gap-2">
                        <Link href={`/dashboard/events/${event.id}`}>
                          <Button variant="outline" size="sm">
                            <Eye className="w-4 h-4 mr-2" />
                            View
                          </Button>
                        </Link>
                        <Link href={`/dashboard/events/new?id=${event.id}`}>
                          <Button variant="outline" size="sm">
                            <Edit className="w-4 h-4 mr-2" />
                            Edit
                          </Button>
                        </Link>
                      </div>
                      <EventActions
                        eventId={event.id}
                        eventTitle={event.title}
                        isPublished={true} />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Draft Events Section */}
      <div className="mb-8" id="draft-events-section">
        <div className="bg-[hsl(var(--card))] text-[hsl(var(--card-foreground))] rounded-lg shadow p-6 border border-[hsl(var(--border))]">
          <h2 className="text-xl font-semibold mb-4">Draft Events</h2>

          {draftEvents.length === 0 ? (
            <div className="border border-[hsl(var(--border))] rounded-lg overflow-hidden">
              <div className="p-8 text-center text-[hsl(var(--muted-foreground))]">
                <p>No draft events found.</p>
                <Link href="/dashboard/events/new">
                  <Button className="mt-4 px-4 py-2 bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))] rounded-md hover:bg-[hsl(var(--primary-hover))] transition">
                    Create Event
                  </Button>
                </Link>
              </div>
            </div>
          ) : (
            <div className="border border-[hsl(var(--border))] rounded-lg overflow-hidden">
              <div className="divide-y divide-[hsl(var(--border))]">
                {draftEvents.map((event) => (
                  <div key={event.id} className="p-4 hover:bg-[hsl(var(--primary-50))] dark:hover:bg-[hsl(var(--primary-subtle))] transition-colors flex justify-between items-center">
                    <div>
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">{event.title}</h3>
                        <Badge variant="outline" className="bg-amber-50 dark:bg-amber-950/30 text-amber-600 dark:text-amber-400 border-amber-200 dark:border-amber-800/50">
                          Draft
                        </Badge>
                      </div>
                      <p className="text-sm text-[hsl(var(--muted-foreground))] mt-1">
                        Last updated {formatDistance(new Date(event.updatedAt), new Date(), { addSuffix: true })}
                      </p>

                      {/* Event Completion Indicator */}
                      <EventCompletionIndicator event={event} />

                      {event.startDate && (
                        <p className="text-xs text-[hsl(var(--muted-foreground))] mt-1">
                          <span className="font-semibold">Start:</span> {new Date(event.startDate).toLocaleString()}
                          {event.endDate && (
                            <> | <span className="font-semibold">End:</span> {new Date(event.endDate).toLocaleString()}</>
                          )}
                        </p>
                      )}
                      {event.city && (
                        <p className="text-xs text-[hsl(var(--muted-foreground))]">
                          <span className="font-semibold">Location:</span> {event.city}, {event.state}, {event.country}
                        </p>
                      )}
                    </div>
                    <div className="flex gap-2 items-center">
                      <Link href={`/dashboard/events/new?id=${event.id}`}>
                        <Button variant="outline" size="sm">
                          <Edit className="w-4 h-4 mr-2" />
                          Continue editing
                        </Button>
                      </Link>
                      <EventActions
                        eventId={event.id}
                        eventTitle={event.title}
                        isPublished={false} />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions Section */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2 bg-[hsl(var(--card))] text-[hsl(var(--card-foreground))] rounded-lg shadow p-6 border border-[hsl(var(--border))]">
          <h2 className="text-xl font-semibold mb-4">Popular Categories</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {['Music', 'Technology', 'Sports', 'Food', 'Arts', 'Business'].map((category) => (
              <div key={category} className="border border-[hsl(var(--border))] rounded-lg p-4 text-center hover:bg-[hsl(var(--primary-50))] dark:hover:bg-[hsl(var(--primary-subtle))] transition-colors cursor-pointer">
                {category}
              </div>
            ))}
          </div>
        </div>
        <div className="bg-[hsl(var(--card))] text-[hsl(var(--card-foreground))] rounded-lg shadow p-6 border border-[hsl(var(--border))]">
          <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
          <div className="space-y-3">
            <Link href="/dashboard/events/new">
              <Button variant="outline" className="w-full text-left justify-start">
                <PlusCircle className="w-4 h-4 mr-2" />
                Create Event
              </Button>
            </Link>
            <Button variant="outline" className="w-full text-left justify-start">
              <Calendar className="w-4 h-4 mr-2" />
              View Calendar
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}