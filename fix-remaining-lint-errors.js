#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Advanced Lint Error Fixer - Phase 2
 * Handles remaining parsing errors and complex syntax issues
 */

// Advanced patterns to fix
const advancedFixes = [
    // JSX closing tag issues
    {
        pattern: /(\s*<\/?)div(\s*>)/g,
        replacement: (match, openTag, closeTag) => {
            // Ensure proper div closing
            if (openTag.includes('</')) {
                return openTag + 'div' + closeTag;
            }
            return match;
        },
        description: 'Fix malformed div closing tags',
        isFunction: true
    },

    // Common parsing syntax errors
    {
        pattern: /(\w+)\s*:\s*(\w+)\s*,\s*expected\./g,
        replacement: '', // This will be handled specially
        description: 'Remove parsing error comments',
        isFunction: false
    },

    // Fix unterminated string literals
    {
        pattern: /(["`'])([^"`']*)\n/g,
        replacement: '$1$2$1\n',
        description: 'Fix unterminated string literals',
        isFunction: false
    },

    // Fix malformed JSX self-closing tags
    {
        pattern: /(<\w+[^>]*)\s*\/?\s*>/g,
        replacement: (match, tagStart) => {
            if (tagStart.includes('=')) {
                return tagStart + ' />';
            }
            return match;
        },
        description: 'Fix malformed self-closing JSX tags',
        isFunction: true
    }
];

// File extensions to process
const extensions = ['.tsx', '.ts', '.jsx', '.js'];

// Directories to exclude
const excludeDirs = ['node_modules', '.next', '.git', 'dist', 'build'];

/**
 * Recursively get all files with specific extensions
 */
function getFiles(dir, fileList = []) {
    const files = fs.readdirSync(dir);

    files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);

        if (stat.isDirectory()) {
            if (!excludeDirs.includes(file)) {
                getFiles(filePath, fileList);
            }
        } else {
            const ext = path.extname(file);
            if (extensions.includes(ext)) {
                fileList.push(filePath);
            }
        }
    });

    return fileList;
}

/**
 * Fix specific parsing errors in individual files
 */
function fixParsingErrors(filePath, content) {
    let fixed = content;
    const appliedFixes = [];

    // Fix comma expected errors (common pattern: function params)
    const commaExpectedPattern = /(\w+)\s*(\w+)\s*\(/g;
    if (fixed.match(commaExpectedPattern)) {
        fixed = fixed.replace(commaExpectedPattern, '$1, $2(');
        appliedFixes.push('Fixed missing commas in function parameters');
    }

    // Fix expected corresponding JSX closing tags
    const openDivPattern = /<div[^>]*>/g;
    const closeDivPattern = /<\/div>/g;
    const openDivCount = (fixed.match(openDivPattern) || []).length;
    const closeDivCount = (fixed.match(closeDivPattern) || []).length;

    if (openDivCount > closeDivCount) {
        const difference = openDivCount - closeDivCount;
        for (let i = 0; i < difference; i++) {
            fixed += '\n</div>';
        }
        appliedFixes.push(`Added ${difference} missing div closing tag(s)`);
    }

    // Fix unterminated string literals (look for quotes at end of lines)
    const unterminatedStrings = /(['"`])([^'"`]*)\n(?!\s*\1)/g;
    const beforeCount = (fixed.match(unterminatedStrings) || []).length;
    if (beforeCount > 0) {
        fixed = fixed.replace(unterminatedStrings, '$1$2$1\n');
        appliedFixes.push(`Fixed ${beforeCount} unterminated string literal(s)`);
    }

    return { content: fixed, fixes: appliedFixes };
}

/**
 * Apply fixes to a single file
 */
function fixFile(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        let modified = false;
        const allFixes = [];

        // Apply advanced pattern fixes
        advancedFixes.forEach(fix => {
            if (fix.isFunction && typeof fix.replacement === 'function') {
                const beforeCount = (content.match(fix.pattern) || []).length;
                if (beforeCount > 0) {
                    content = content.replace(fix.pattern, fix.replacement);
                    allFixes.push(`${fix.description} (${beforeCount} instances)`);
                    modified = true;
                }
            } else if (!fix.isFunction) {
                const beforeCount = (content.match(fix.pattern) || []).length;
                if (beforeCount > 0) {
                    content = content.replace(fix.pattern, fix.replacement);
                    allFixes.push(`${fix.description} (${beforeCount} instances)`);
                    modified = true;
                }
            }
        });

        // Apply parsing error fixes
        const parsingResult = fixParsingErrors(filePath, content);
        if (parsingResult.fixes.length > 0) {
            content = parsingResult.content;
            allFixes.push(...parsingResult.fixes);
            modified = true;
        }

        // Write back if modified
        if (modified) {
            fs.writeFileSync(filePath, content, 'utf8');
            console.log(`✅ Fixed: ${filePath}`);
            allFixes.forEach(fix => console.log(`   - ${fix}`));
        }

        return modified;
    } catch (error) {
        console.error(`❌ Error processing ${filePath}:`, error.message);
        return false;
    }
}

/**
 * Main execution
 */
function main() {
    console.log('🚀 Starting Phase 2: Advanced lint error fixes...\n');

    // Get files with known parsing errors from the lint output
    const problematicFiles = [
        'src/app/actions/activity.ts',
        'src/app/actions/apply-migration.ts',
        'src/app/actions/contacts.ts',
        'src/app/actions/data-export.ts',
        'src/app/actions/event-actions.ts',
        'src/app/dashboard/events/[id]/not-found.tsx',
        'src/app/events/registration-success/page.tsx',
        'src/components/auth/GoogleSignInButton.tsx',
        'src/components/auth/SignInForm.tsx',
        'src/components/auth/SignUpForm.tsx',
        'src/components/cookie/cookie-banner.tsx',
        'src/components/cookie/cookie-preferences.tsx',
        'src/components/events/event-wizard/steps/basic-details-step.tsx',
        'src/components/events/event-wizard/steps/fields-step.tsx',
        'src/components/events/event-wizard/steps/image-upload-step.tsx',
        'src/components/ui/combobox.tsx',
        'src/lib/db/base-repository.ts'
    ];

    console.log(`🎯 Targeting ${problematicFiles.length} files with known parsing errors\n`);

    let fixedCount = 0;
    let totalFiles = 0;

    // Process each problematic file
    problematicFiles.forEach(relativePath => {
        const filePath = path.join(process.cwd(), relativePath);
        if (fs.existsSync(filePath)) {
            totalFiles++;
            const wasFixed = fixFile(filePath);
            if (wasFixed) {
                fixedCount++;
            }
        } else {
            console.log(`⚠️  File not found: ${relativePath}`);
        }
    });

    console.log('\n📊 Phase 2 Summary:');
    console.log(`   Files processed: ${totalFiles}`);
    console.log(`   Files modified: ${fixedCount}`);
    console.log(`   Files unchanged: ${totalFiles - fixedCount}`);

    if (fixedCount > 0) {
        console.log('\n🎉 Phase 2 complete! Run "pnpm lint" to check remaining issues.');
    } else {
        console.log('\n✨ No additional automatic fixes applied.');
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = { fixFile, fixParsingErrors }; 