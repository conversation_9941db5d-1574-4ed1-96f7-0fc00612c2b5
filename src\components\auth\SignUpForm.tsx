'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { signUpWithEmail, isEventRegistrationUrl } from '@/lib/supabase/auth'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import Link from 'next/link'
import GoogleSignInButton from './GoogleSignInButton'
import { getBaseUrl } from '@/utils/url-utilities'
import { logger } from '@/lib/logger';
import { useAuth } from '@/contexts/AuthContext'
import { toast } from '@/components/ui/use-toast'

export function SignUpForm() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const [success, setSuccess] = useState(false)
  const router = useRouter()
  const searchParams = useSearchParams()
  const { signUp } = useAuth()
  let redirectUrl = searchParams?.get('redirect_url') || '/dashboard'

  // Prevent redirect loops with auth callback
  if (redirectUrl.includes('/auth/callback')) {
    console.warn('Detected potential auth redirect loop in SignUpForm, redirecting to dashboard')
    redirectUrl = '/dashboard'
  }

  // Log if this is an event registration URL
  useEffect(() => {
    if (isEventRegistrationUrl(redirectUrl)) {
      logger.info('Sign-up initiated from event registration page:', redirectUrl);
    }
  }, [redirectUrl])

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setLoading(true)

    try {
      const formData = new FormData(e.currentTarget)
      const email = formData.get('email') as string
      const password = formData.get('password') as string
      const confirmPassword = formData.get('confirmPassword') as string

      if (password !== confirmPassword) {
        toast({
          title: 'Error',
          description: 'Passwords do not match',
          variant: 'destructive',
        })
        return
      }

      await signUp({ email, password })
      router.push('/dashboard')
    } catch (error) {
      console.error('Sign up error:', error)
      toast({
        title: 'Error',
        description: 'Failed to create account. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  if (success) {
    return (
      <div className="w-full max-w-md mx-auto space-y-6 text-foreground">
        <div className="text-center">
          <h1 className="text-2xl font-bold">Check your email</h1>
          <p className="text-muted-foreground mt-2">
            We've sent a confirmation link to <strong className="text-foreground">{email}</strong>.
            Please check your email and click the link to complete your registration.
          </p>
          <p className="text-muted-foreground mt-2">
            After verification, you'll be redirected to your original location.
          </p>
        </div>

        <Button
          className="w-full"
          onClick={() => router.push('/sign-in')}
        >
          Back to Sign In
        </Button>
      </div>
    )
  }

  return (
    <div className="w-full max-w-md mx-auto space-y-6 text-foreground">
      <div className="text-center">
        <h1 className="text-2xl font-semibold tracking-tight">
          Create an account
        </h1>
        <p className="text-sm text-muted-foreground">
          Enter your email below to create your account
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="email" className="text-foreground">Email</Label>
          <Input
            id="email"
            name="email"
            type="email"
            placeholder="<EMAIL>"
            required
            className="w-full"
            disabled={loading}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="password" className="text-foreground">Password</Label>
          <Input
            id="password"
            name="password"
            type="password"
            required
            className="w-full"
            disabled={loading}
          />
          <p className="text-sm text-muted-foreground">
            Password must be at least 8 characters long
          </p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="confirmPassword" className="text-foreground">Confirm Password</Label>
          <Input
            id="confirmPassword"
            name="confirmPassword"
            type="password"
            required
            className="w-full"
            disabled={loading}
          />
        </div>

        <Button
          type="submit"
          className="w-full"
          disabled={loading}
        >
          {loading ? 'Creating account...' : 'Create Account'}
        </Button>
      </form>

      <div className="relative my-6">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">
            Or continue with
          </span>
        </div>
      </div>

      <GoogleSignInButton
        label="Sign up with Google"
        redirectTo={redirectUrl} />

      <div className="text-center text-sm">
        Already have an account?{' '}
        <Link href="/signin" className="font-medium hover:underline">
          Sign in
        </Link>
      </div>
    </div>
  )
}
