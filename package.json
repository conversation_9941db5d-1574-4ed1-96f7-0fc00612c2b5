{"name": "fuiyoo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "dev:turbo": "next dev --turbo", "dev:standard": "next dev", "dev:turbo-max": "NODE_OPTIONS='--max-old-space-size=8192' next dev --turbo", "dev:turbo-debug": "NEXT_TURBOPACK_TRACING=1 next dev --turbo", "dev:turbo-safe": "./dev.sh --turbo --safe", "prebuild:disabled": "eslint .", "build": "NODE_OPTIONS='--max-old-space-size=8192' next build", "build:prod": "next build", "build:turbo": "NODE_OPTIONS='--max-old-space-size=8192' next build --turbo", "build:force": "next build --force", "build:safe": "NEXT_SKIP_PREFLIGHT_CHECK=true next build", "build:analyze": "ANALYZE=true next build", "start": "next start", "lint": "next lint", "db:migrate": "tsx src/scripts/migrate.ts up", "db:create-migration": "tsx src/scripts/migrate.ts create", "db:status": "tsx src/scripts/migrate.ts status", "db:refresh-schema": "tsx src/scripts/migrate.ts refresh", "clean": "rm -rf .next && rm -rf node_modules/.cache", "type-check": "tsc --noEmit", "lint:fix": "next lint --fix", "validate": "pnpm run lint && pnpm run type-check", "prepare": "husky", "migrate:create": "node scripts/create-migration.js", "migrate:apply": "node scripts/apply-migration.js"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^5.0.1", "@paralleldrive/cuid2": "^2.2.2", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-tooltip": "^1.2.6", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@tailwindcss/postcss": "^4.1.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.10.0", "lodash": "^4.17.21", "lucide-react": "^0.487.0", "netlify-cli": "^21.1.0", "next": "15.3.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-day-picker": "^9.6.7", "react-dom": "^19.1.0", "react-hook-form": "^7.56.2", "react-select": "^5.10.1", "react-type-animation": "^3.2.0", "sonner": "^2.0.3", "tailwind-merge": "^2.6.0", "tailwindcss": "^4.1.5", "uuid": "^11.1.0", "zod": "^3.24.4", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/compat": "^1.2.9", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.26.0", "@netlify/plugin-nextjs": "^5.11.1", "@next/bundle-analyzer": "^15.3.2", "@next/eslint-plugin-next": "^15.3.2", "@svgr/webpack": "^8.1.0", "@types/lodash": "^4.17.16", "@types/node": "^20.17.43", "@types/react": "^19.1.3", "@types/react-dom": "^19.1.3", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "autoprefixer": "^10.4.21", "critters": "^0.0.25", "dotenv": "^16.5.0", "eslint": "^8.57.1", "eslint-config-next": "14.1.0", "eslint-plugin-react-hooks": "^5.2.0", "husky": "^9.1.7", "pg": "^8.15.6", "postcss": "^8.5.3", "ts-node": "^10.9.2", "tsx": "^4.19.4", "typescript": "^5.8.3"}}