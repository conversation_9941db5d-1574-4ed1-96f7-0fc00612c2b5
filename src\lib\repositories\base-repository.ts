import { createClient } from '@/lib/supabase/pages-client';
import { PostgrestSingleResponse, PostgrestResponse } from '@supabase/supabase-js';
import { Database } from '@/types/supabase';

type TableName = keyof Database['public']['Tables'];
type TableRow<T extends TableName> = Database['public']['Tables'][T]['Row'];
type TableInsert<T extends TableName> = Database['public']['Tables'][T]['Insert'];
type TableUpdate<T extends TableName> = Database['public']['Tables'][T]['Update'];

/**
 * Base repository for database operations
 */
export class BaseRepository<T extends TableName> {
  protected tableName: T;

  /**
   * Create a new repository
   * @param tableName Database table name
   */
  constructor(tableName: T) {
    this.tableName = tableName;
  }

  /**
   * Get the Supabase client
   */
  protected async getClient() {
    return await createClient();
  }

  /**
   * Find a record by ID
   */
  async findById(id: string): Promise<TableRow<T> | null> {
    try {
      const supabase = await this.getClient();

      const { data, error } = await supabase
        .from(this.tableName)
        .select()
        .match({ id } as any)
        .single() as PostgrestSingleResponse<TableRow<T>>;

      if (error) {
        console.error(`Error fetching ${this.tableName} by ID:`, error);
        return null;
      }

      return data;
    } catch (error) {
      console.error(`Error in findById for ${this.tableName}:`, error);
      return null;
    }
  }

  /**
   * Find records by a query
   */
  async find(query: Partial<TableRow<T>> = {}): Promise<TableRow<T>[]> {
    try {
      const supabase = await this.getClient();

      let queryBuilder = supabase.from(this.tableName).select();

      // Apply filters
      Object.entries(query).forEach(([key, value]) => {
        if (value !== undefined) {
          queryBuilder = queryBuilder.match({ [key]: value } as any);
        }
      });

      const { data, error } = await queryBuilder as PostgrestResponse<TableRow<T>>;

      if (error) {
        console.error(`Error fetching ${this.tableName}:`, error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error(`Error in find for ${this.tableName}:`, error);
      return [];
    }
  }

  /**
   * Create a new record
   */
  async create(data: TableInsert<T>): Promise<TableRow<T> | null> {
    try {
      const supabase = await this.getClient();

      const { data: result, error } = await supabase
        .from(this.tableName)
        .insert(data as any)
        .select()
        .single() as PostgrestSingleResponse<TableRow<T>>;

      if (error) {
        console.error(`Error creating ${this.tableName}:`, error);
        return null;
      }

      return result;
    } catch (error) {
      console.error(`Error in create for ${this.tableName}:`, error);
      return null;
    }
  }

  /**
   * Update a record
   */
  async update(id: string, data: TableUpdate<T>): Promise<TableRow<T> | null> {
    try {
      const supabase = await this.getClient();

      const { data: result, error } = await supabase
        .from(this.tableName)
        .update(data as any)
        .match({ id } as any)
        .select()
        .single() as PostgrestSingleResponse<TableRow<T>>;

      if (error) {
        console.error(`Error updating ${this.tableName}:`, error);
        return null;
      }

      return result;
    } catch (error) {
      console.error(`Error in update for ${this.tableName}:`, error);
      return null;
    }
  }

  /**
   * Delete a record
   */
  async delete(id: string): Promise<boolean> {
    try {
      const supabase = await this.getClient();

      const { error } = await supabase
        .from(this.tableName)
        .delete()
        .match({ id } as any);

      if (error) {
        console.error(`Error deleting ${this.tableName}:`, error);
        return false;
      }

      return true;
    } catch (error) {
      console.error(`Error in delete for ${this.tableName}:`, error);
      return false;
    }
  }
}
