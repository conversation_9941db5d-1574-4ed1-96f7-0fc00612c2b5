const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Get the migration name from command line arguments
const migrationName = process.argv[2];
if (!migrationName) {
    console.error('Please provide a migration name');
    process.exit(1);
}

// Load environment variables
require('dotenv').config();

// Create Supabase client
const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Read migration file
const migrationsDir = path.join(__dirname, '../src/migrations');
const migrationFiles = fs.readdirSync(migrationsDir);
const migrationFile = migrationFiles.find(file => file.includes(migrationName));

if (!migrationFile) {
    console.error(`Migration ${migrationName} not found`);
    process.exit(1);
}

const filePath = path.join(migrationsDir, migrationFile);
const migration = fs.readFileSync(filePath, 'utf8');

// Extract up and down migrations
const upMatch = migration.match(/-- Up\n([\s\S]*?)(?=-- Down|$)/);
const downMatch = migration.match(/-- Down\n([\s\S]*?)$/);

const up = upMatch ? upMatch[1].trim() : '';
const down = downMatch ? downMatch[1].trim() : '';

if (!up) {
    console.error('No up migration found');
    process.exit(1);
}

// Apply migration
async function applyMigration() {
    try {
        // Create migrations table if it doesn't exist
        await supabase.rpc('create_migrations_table');

        // Check if migration has already been applied
        const { data: existingMigration } = await supabase
            .from('migrations')
            .select('*')
            .eq('name', migrationFile)
            .single();

        if (existingMigration) {
            console.error(`Migration ${migrationFile} has already been applied`);
            process.exit(1);
        }

        // Apply migration
        const { error } = await supabase.rpc('run_sql', { sql: up });
        if (error) {
            throw error;
        }

        // Record migration
        await supabase
            .from('migrations')
            .insert({
                name: migrationFile,
                up,
                down,
                applied_at: new Date().toISOString()
            });

        console.log(`Applied migration: ${migrationFile}`);
    } catch (error) {
        console.error('Error applying migration:', error);
        process.exit(1);
    }
}

applyMigration(); 