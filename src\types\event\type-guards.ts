/**
 * Type guards for field types
 * @module types/event/type-guards
 */

import { FieldType } from './field-base';
import type {
    EventField,
    TextField,
    NumberField,
    SelectField,
    DisplayField,
    DateField,
    FileField,
    CustomField,
    FieldOption,
    InputFieldBase
} from './field-types';

const TEXT_FIELD_TYPES = [
    FieldType.TEXT,
    FieldType.TEXTAREA,
    FieldType.EMAIL,
    FieldType.PHONE
] as const;

const SELECT_FIELD_TYPES = [
    FieldType.SELECT,
    FieldType.MULTISELECT,
    FieldType.CHECKBOX,
    FieldType.RADIO
] as const;

const DISPLAY_FIELD_TYPES = [
    FieldType.HEADING,
    FieldType.PARAGRAPH,
    FieldType.DIVIDER
] as const;

const DATE_FIELD_TYPES = [
    FieldType.DATE,
    FieldType.TIME,
    FieldType.DATETIME
] as const;

const FILE_FIELD_TYPES = [
    FieldType.FILE,
    FieldType.IMAGE
] as const;

/**
 * Type guard for text fields
 */
export function isTextField(field: EventField): field is TextField {
    return TEXT_FIELD_TYPES.includes(field.type as typeof TEXT_FIELD_TYPES[number]);
}

/**
 * Type guard for number fields
 */
export function isNumberField(field: EventField): field is NumberField {
    return field.type === FieldType.NUMBER;
}

/**
 * Type guard for select fields
 */
export function isSelectField(field: EventField): field is SelectField {
    return SELECT_FIELD_TYPES.includes(field.type as typeof SELECT_FIELD_TYPES[number]);
}

/**
 * Type guard for display fields
 */
export function isDisplayField(field: EventField): field is DisplayField {
    return DISPLAY_FIELD_TYPES.includes(field.type as typeof DISPLAY_FIELD_TYPES[number]);
}

/**
 * Type guard for date fields
 */
export function isDateField(field: EventField): field is DateField {
    return DATE_FIELD_TYPES.includes(field.type as typeof DATE_FIELD_TYPES[number]);
}

/**
 * Type guard for file fields
 */
export function isFileField(field: EventField): field is FileField {
    return FILE_FIELD_TYPES.includes(field.type as typeof FILE_FIELD_TYPES[number]);
}

/**
 * Type guard for custom fields
 */
export function isCustomField(field: EventField): field is CustomField {
    return field.type === FieldType.CUSTOM;
}

/**
 * Type guard for input fields
 */
export function isInputField(field: EventField): field is EventField & InputFieldBase {
    return !isDisplayField(field);
}

/**
 * Type guard for string options
 */
export function isStringOptions(options: FieldOption[]): options is string[] {
    return options.every(option => typeof option === 'string');
}

/**
 * Type guard for object options
 */
export function isObjectOptions(options: FieldOption[]): options is { label: string; value: string }[] {
    return options.every(option => typeof option === 'object' && option !== null && 'label' in option && 'value' in option);
}

/**
 * Get display value for an option
 */
export function getOptionDisplayValue(option: FieldOption): string {
    if (typeof option === 'string') {
        return option;
    }
    return option.label;
}

/**
 * Type guard for string options
 */
export function isStringOption(option: FieldOption): option is string {
    return typeof option === 'string';
}

/**
 * Type guard for object options
 */
export function isObjectOption(option: FieldOption): option is { label: string; value: string } {
    return typeof option === 'object' && option !== null && 'label' in option && 'value' in option;
}

/**
 * Helper function to get the value of an option
 */
export function getOptionValue(option: FieldOption): string {
    return isStringOption(option) ? option : option.value;
}

/**
 * Gets the appropriate type guard for a field type
 */
export function getTypeGuard(type: FieldType): (field: EventField) => boolean {
    switch (type) {
        case FieldType.TEXT:
        case FieldType.TEXTAREA:
        case FieldType.EMAIL:
        case FieldType.PHONE:
            return isTextField;
        case FieldType.NUMBER:
            return isNumberField;
        case FieldType.SELECT:
        case FieldType.MULTISELECT:
        case FieldType.CHECKBOX:
        case FieldType.RADIO:
            return isSelectField;
        case FieldType.HEADING:
        case FieldType.PARAGRAPH:
        case FieldType.DIVIDER:
            return isDisplayField;
        case FieldType.DATE:
        case FieldType.TIME:
        case FieldType.DATETIME:
            return isDateField;
        case FieldType.FILE:
        case FieldType.IMAGE:
            return isFileField;
        case FieldType.CUSTOM:
            return isCustomField;
        default: {
            const _exhaustiveCheck: never = type;
            throw new Error(`Unsupported field type: ${type}`);
        }
    }
} 