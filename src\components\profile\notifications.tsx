'use client'

import { useState } from 'react'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { toast } from '@/components/ui/use-toast'
import { User } from '@/types/auth'

interface NotificationsProps {
    user: User
}

export function Notifications({ user }: NotificationsProps) {
    const [eventUpdates, setEventUpdates] = useState(true)
    const [eventReminders, setEventReminders] = useState(true)
    const [newMessages, setNewMessages] = useState(true)
    const [loading, setLoading] = useState(false)

    const handleSave = async () => {
        setLoading(true)
        try {
            // Save notification settings to backend
            toast({
                title: 'Success',
                description: 'Your notification settings have been saved.',
                variant: 'success',
            })
        } catch (error) {
            console.error('Error saving notification settings:', error)
            toast({
                title: 'Error',
                description: 'Failed to save notification settings. Please try again.',
                variant: 'destructive',
            })
        } finally {
            setLoading(false)
        }
    }

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                    <Label>Event Updates</Label>
                    <p className="text-sm text-muted-foreground">
                        Get notified about changes to events you&apos;re attending
                    </p>
                </div>
                <Switch
                    checked={eventUpdates}
                    onCheckedChange={setEventUpdates}
                    disabled={loading}
                />
            </div>

            <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                    <Label>Event Reminders</Label>
                    <p className="text-sm text-muted-foreground">
                        Receive reminders before your events start
                    </p>
                </div>
                <Switch
                    checked={eventReminders}
                    onCheckedChange={setEventReminders}
                    disabled={loading}
                />
            </div>

            <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                    <Label>New Messages</Label>
                    <p className="text-sm text-muted-foreground">
                        Get notified when you receive new messages
                    </p>
                </div>
                <Switch
                    checked={newMessages}
                    onCheckedChange={setNewMessages}
                    disabled={loading}
                />
            </div>

            <Button
                onClick={handleSave}
                disabled={loading}
                className="w-full"
            >
                {loading ? 'Saving...' : 'Save Settings'}
            </Button>
        </div>
    )
} 