'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, Trash2, MoreH<PERSON>zon<PERSON>, Eye, EyeOff, Edit } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { SimpleDropdown, SimpleDropdownItem } from '@/components/ui/simple-dropdown';
import Link from 'next/link';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { deleteEvent, duplicateEvent, publishEvent, unpublishEvent } from '@/app/actions/events';
import { useToast } from '@/components/ui/use-toast';
import { useRouter } from 'next/navigation';

interface EventActionsProps {
  eventId: string;
  eventTitle: string;
  isPublished?: boolean;
}

export function EventActions({ eventId, eventTitle, isPublished = false }: EventActionsProps) {
  const [isDeleteD<PERSON>ogO<PERSON>, setIsDeleteDialogOpen] = useState(false);
  const [isDuplicateDialogOpen, setIsDuplicateDialogOpen] = useState(false);
  const [isPublishDialogOpen, setIsPublishDialogOpen] = useState(false);
  const [isUnpublishDialogOpen, setIsUnpublishDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const router = useRouter();

  const handleDelete = async () => {
    setIsLoading(true);
    try {
      const result = await deleteEvent(eventId);
      if (result.success) {
        toast({
          title: 'Event deleted',
          description: `"${eventTitle}" has been deleted successfully.`,
        });
        router.refresh();
      } else {
        toast({
          title: 'Error',
          description: result.error || 'Failed to delete event',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
      setIsDeleteDialogOpen(false);
    }
  };

  const handleDuplicate = async () => {
    setIsLoading(true);
    try {
      const result = await duplicateEvent(eventId);
      if (result.success) {
        toast({
          title: 'Event duplicated',
          description: `"${eventTitle}" has been duplicated successfully.`,
        });
        router.refresh();
      } else {
        toast({
          title: 'Error',
          description: result.error || 'Failed to duplicate event',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
      setIsDuplicateDialogOpen(false);
    }
  };

  const handlePublish = async () => {
    setIsLoading(true);
    try {
      const result = await publishEvent(eventId);
      if (result.success) {
        toast({
          title: 'Event published',
          description: `"${eventTitle}" has been published successfully.`,
        });
        router.refresh();
      } else {
        toast({
          title: 'Error',
          description: result.error || 'Failed to publish event',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
      setIsPublishDialogOpen(false);
    }
  };

  const handleUnpublish = async () => {
    setIsLoading(true);
    try {
      const result = await unpublishEvent(eventId);
      if (result.success) {
        toast({
          title: 'Event unpublished',
          description: `"${eventTitle}" has been unpublished successfully.`,
        });
        router.refresh();
      } else {
        toast({
          title: 'Error',
          description: result.error || 'Failed to unpublish event',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
      setIsUnpublishDialogOpen(false);
    }
  };

  return (
    <>
      <SimpleDropdown
        trigger={
          <>
            <MoreHorizontal className="h-4 w-4" />
            <span className="sr-only">More options</span>
          </>
        }
      >
        <SimpleDropdownItem>
          <Link href={`/dashboard/events/new?id=${eventId}&no_redirect=true`} className="flex items-center">
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </Link>
        </SimpleDropdownItem>

        <SimpleDropdownItem onClick={() => setIsDuplicateDialogOpen(true)}>
          <Copy className="mr-2 h-4 w-4" />
          Duplicate
        </SimpleDropdownItem>

        {isPublished ? (
          <SimpleDropdownItem onClick={() => setIsUnpublishDialogOpen(true)}>
            <EyeOff className="mr-2 h-4 w-4" />
            Unpublish
          </SimpleDropdownItem>
        ) : (
          <SimpleDropdownItem onClick={() => setIsPublishDialogOpen(true)}>
            <Eye className="mr-2 h-4 w-4" />
            Publish
          </SimpleDropdownItem>
        )}

        <SimpleDropdownItem
          onClick={() => setIsDeleteDialogOpen(true)}
          destructive
        >
          <Trash2 className="mr-2 h-4 w-4" />
          Delete
        </SimpleDropdownItem>
      </SimpleDropdown>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to delete this event?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the event
              "{eventTitle}" and all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading} className="bg-secondary text-secondary-foreground hover:bg-secondary/90">
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                handleDelete();
              }}
              disabled={isLoading}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isLoading ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Duplicate Confirmation Dialog */}
      <AlertDialog open={isDuplicateDialogOpen} onOpenChange={setIsDuplicateDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Duplicate this event?</AlertDialogTitle>
            <AlertDialogDescription>
              This will create a copy of "{eventTitle}" as a draft event.
              {isPublished && ' All settings and details will be copied, but the new event will start as a draft.'}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              disabled={isLoading}
              className="bg-secondary text-secondary-foreground hover:bg-secondary/90"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                handleDuplicate();
              }}
              disabled={isLoading}
              className="bg-primary text-primary-foreground hover:bg-primary/90"
            >
              {isLoading ? 'Duplicating...' : 'Duplicate'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Publish Confirmation Dialog */}
      <AlertDialog open={isPublishDialogOpen} onOpenChange={setIsPublishDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Publish this event?</AlertDialogTitle>
            <AlertDialogDescription>
              This will make "{eventTitle}" visible to the public.
              Make sure all event details are complete and accurate.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              disabled={isLoading}
              className="bg-secondary text-secondary-foreground hover:bg-secondary/90"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                handlePublish();
              }}
              disabled={isLoading}
              className="bg-primary text-primary-foreground hover:bg-primary/90"
            >
              {isLoading ? 'Publishing...' : 'Publish'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Unpublish Confirmation Dialog */}
      <AlertDialog open={isUnpublishDialogOpen} onOpenChange={setIsUnpublishDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Unpublish this event?</AlertDialogTitle>
            <AlertDialogDescription>
              This will hide "{eventTitle}" from the public.
              Users will no longer be able to view or register for this event.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              disabled={isLoading}
              className="bg-secondary text-secondary-foreground hover:bg-secondary/90"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                handleUnpublish();
              }}
              disabled={isLoading}
              className="bg-primary text-primary-foreground hover:bg-primary/90"
            >
              {isLoading ? 'Unpublishing...' : 'Unpublish'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
