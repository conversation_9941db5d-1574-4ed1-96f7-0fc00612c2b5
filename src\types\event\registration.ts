/**
 * Event registration type definitions
 * @module types/event/registration
 */

import { FieldType } from './field';

/**
 * Registration field definition
 */
export interface RegistrationField {
    /** Unique identifier for the field */
    id: string;
    /** Associated event ID */
    eventId: string;
    /** Field identifier */
    fieldId: string;
    /** Type of the field */
    fieldType: FieldType;
    /** Display label */
    label: string;
    /** Optional description/help text */
    description?: string;
    /** Whether the field is required */
    isRequired: boolean;
    /** Whether the field is publicly visible */
    isPublic: boolean;
    /** Field validation rules */
    validationRules?: Record<string, unknown>;
    /** Default field value */
    defaultValue?: unknown;
    /** Field options for select/radio fields */
    options?: unknown;
    /** Display order */
    orderIndex: number;
    /** Creation timestamp */
    createdAt: Date;
    /** Last update timestamp */
    updatedAt: Date;
}

/**
 * Emergency contact settings
 */
export interface EmergencyContactSettings {
    /** Whether emergency contact is required */
    required: boolean;
    /** Required emergency contact fields */
    fields: string[];
    /** Whether the same contact can be used for multiple registrations */
    allowSameForMultipleRegistrations: boolean;
}

/**
 * Registration field mapping
 */
export interface FieldMapping {
    /** Unique identifier for the mapping */
    id: string;
    /** Associated event ID */
    eventId: string;
    /** Field identifier */
    fieldId: string;
    /** Profile field to map to */
    profileField: string;
    /** Whether the mapping is bidirectional */
    isBidirectional: boolean;
    /** Creation timestamp */
    createdAt: Date;
    /** Last update timestamp */
    updatedAt: Date;
} 