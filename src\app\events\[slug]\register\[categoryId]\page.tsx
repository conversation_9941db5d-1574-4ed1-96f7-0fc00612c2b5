import React from 'react';
import Link from 'next/link';
import { notFound, redirect } from 'next/navigation';
import { ArrowLeft, ArrowRight, Calendar, DollarSign, Tag } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { EventRepository } from '@/repositories/event-repository';
import { EventCategoryRepository } from '@/repositories/event-category-repository';
import { createClient } from '@/lib/supabase/server';
import { formatDate } from '@/utils/formatDate';
import { formatCurrency, getCurrencyInfo } from '@/lib/utils/currency-utils';
import { logger } from '@/lib/logger';

interface CategoryRegistrationProps {
  params: Promise<{
    slug: string;
    categoryId: string;
  }>;
}

export default async function CategoryRegistrationPage({ params }: CategoryRegistrationProps) {
  // Await params to fix the "params should be awaited" error in Next.js 14+
  const { slug, categoryId } = await params;

  // Fetch the event from the database
  const eventRepository = new EventRepository();
  const event = await eventRepository.getEventBySlug(slug);

  if (!event || event.status !== 'published') {
    notFound();
  }

  // Fetch the category separately
  const categoryRepository = new EventCategoryRepository();
  const categories = await categoryRepository.getCategoriesByEventId(event.id);
  const selectedCategory = categories.find((cat) => cat.id === categoryId);

  // Log event country and currency info
  logger.info(`Event country code: "${event.country}" (type: ${typeof event.country})`);
  const currencyInfo = getCurrencyInfo(event.country);
  logger.info(`Currency info for ${event.country}:`, currencyInfo);
  logger.info(`Selected category for registration:`, {
    id: selectedCategory?.id,
    name: selectedCategory?.name,
    price: selectedCategory?.properties?.price
  });
  if (!selectedCategory) {
    notFound();
  }

  // Check if registration is open
  const now = new Date();

  // Check category-specific closing date if available
  const regCloseDateProp = selectedCategory.properties?.registrationCloseDate;
  const categoryCloseDate = typeof regCloseDateProp === 'string' && regCloseDateProp
    ? new Date(regCloseDateProp)
    : null;

  // Fall back to event-level closing date
  const eventCloseDate = event.registrationCloseDate
    ? new Date(event.registrationCloseDate)
    : null;

  const registrationCloseDate = categoryCloseDate || eventCloseDate;

  if (registrationCloseDate && now > registrationCloseDate) {
    // Registration is closed
    return (
      <div className="container max-w-4xl mx-auto px-4 py-12">
        <Link href={`/events/${slug}/register`} className="flex items-center text-sm text-muted-foreground hover:text-foreground mb-8">
          <ArrowLeft className="w-4 h-4 mr-1" />
          Back to Categories
        </Link>

        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Registration Closed</CardTitle>
            <CardDescription>
              Registration for this category has ended on {formatDate(registrationCloseDate)}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="mb-6">
              The registration period for "{selectedCategory.name}" has ended. Please contact the event organizer for more information or select a different category.
            </p>
            <Button asChild>
              <Link href={`/events/${slug}/register`}>
                View Other Categories
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Check if category is full
  const registrationLimit = selectedCategory.properties?.registrationLimit;
  const registrationCount = selectedCategory.properties?.registrationCount || 0;

  if (registrationLimit && registrationCount >= registrationLimit) {
    // Category is full
    return (
      <div className="container max-w-4xl mx-auto px-4 py-12">
        <Link href={`/events/${slug}/register`} className="flex items-center text-sm text-muted-foreground hover:text-foreground mb-8">
          <ArrowLeft className="w-4 h-4 mr-1" />
          Back to Categories
        </Link>

        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Category Full</CardTitle>
            <CardDescription>
              This category has reached its maximum capacity
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="mb-6">
              The category "{selectedCategory.name}" is currently full. Please select a different category or contact the event organizer for more information.
            </p>
            <Button asChild>
              <Link href={`/events/${slug}/register`}>
                View Other Categories
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Check if user is authenticated
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();

  // If not authenticated, redirect to sign in
  if (!user) {
    // Store the current URL to redirect back after sign in
    redirect(`/sign-in?redirect=/events/${slug}/register/${categoryId}`);
  }

  // Get user profile
  const { data: userProfile } = await supabase
    .from('users')
    .select('*')
    .eq('auth_user_id', user.id)
    .single();

  // Log user profile data for debugging
  if (process.env.NODE_ENV === 'development') {

    logger.debug('User profile data:', {
      contactNo: userProfile?.contactNo,
      phone: userProfile?.contactNo, // Using contactNo instead of phone
      tshirtSize: userProfile?.tshirt_size, // Using tshirt_size from database
      tshirt_size: userProfile?.tshirt_size,
      userId: user.id,
      authUserId: user.id, // Using user.id as auth_user_id
      emergencyContactName: userProfile?.emergencyContactName,
      emergencyContactNo: userProfile?.emergencyContactNo,
      emergencyContactRelationship: userProfile?.emergencyContactRelationship
    });

  }
  return (
    <div className="container max-w-4xl mx-auto px-4 py-12">
      <div className="flex justify-between items-center mb-6">
        <Link href={`/events/${slug}/register`} className="inline-flex items-center text-sm bg-muted hover:bg-muted/80 px-3 py-2 rounded-md transition-colors">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Categories
        </Link>

        <Link href={`/events/${slug}`} className="inline-flex items-center text-sm text-muted-foreground hover:text-foreground">
          View Event Details
          <ArrowRight className="w-4 h-4 ml-1" />
        </Link>
      </div>

      <div className="mb-8 bg-gradient-to-r from-indigo-50 to-indigo-100 dark:from-indigo-950/30 dark:to-indigo-900/20 p-4 rounded-lg border border-indigo-200 dark:border-indigo-800">
        <h1 className="text-3xl font-bold mb-3">Register for {event.title}</h1>
        <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
          <div className="flex items-center">
            <Tag className="w-4 h-4 text-indigo-600 dark:text-indigo-400 mr-2" />
            <span className="font-medium">{selectedCategory.name}</span>
          </div>

          <div className="flex items-center">
            <DollarSign className="w-4 h-4 text-indigo-600 dark:text-indigo-400 mr-2" />
            {((): React.ReactNode => {
              const priceProp = selectedCategory.properties?.price;
              const priceString = typeof priceProp === 'number' ? String(priceProp) : (typeof priceProp === 'string' ? priceProp : null);
              if (priceString) {
                return <span className="font-medium">{formatCurrency(parseFloat(priceString), event.country)}</span>;
              }
              return <span className="font-medium text-green-600">Free Entry</span>;
            })()}
          </div>

          {event.startDate && (
            <div className="flex items-center">
              <Calendar className="w-4 h-4 text-indigo-600 dark:text-indigo-400 mr-2" />
              <span>{formatDate(event.startDate)}</span>
            </div>
          )}
        </div>
      </div>

      <form action="/api/events/register" method="POST">
        <input type="hidden" name="eventId" value={event.id} />
        <input type="hidden" name="categoryId" value={categoryId} />

        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Personal Information</CardTitle>
            <CardDescription>
              Please provide your personal information for registration
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  name="firstName"
                  defaultValue={userProfile?.first_name || ''}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  name="lastName"
                  defaultValue={userProfile?.last_name || ''}
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                name="email"
                type="email"
                defaultValue={user.email || ''}
                required
                readOnly
              />
              <p className="text-xs text-muted-foreground">
                This is your account email and cannot be changed
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                name="phone"
                type="tel"
                defaultValue={userProfile?.contactNo || ''}
                required
              />
              {userProfile?.contactNo && (
                <p className="text-xs text-muted-foreground">
                  Auto-filled from your profile
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Emergency Contact Information - only shown if required by the event */}
        {event.emergencyContactSettings?.required && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-heart-pulse">
                  <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z" />
                  <path d="M3.22 12H9.5l.5-1 2 4 .5-1h6.78" />
                </svg>
                Emergency Contact Information
              </CardTitle>
              <CardDescription>
                Please provide emergency contact details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="emergencyContactName">Emergency Contact Name</Label>
                <Input
                  id="emergencyContactName"
                  name="emergencyContactName"
                  defaultValue={userProfile?.emergencyContactName || ''}
                  required
                />
                {userProfile?.emergencyContactName && (
                  <p className="text-xs text-muted-foreground">
                    Auto-filled from your profile
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="emergencyContactNo">Emergency Contact Phone Number</Label>
                <Input
                  id="emergencyContactNo"
                  name="emergencyContactNo"
                  type="tel"
                  defaultValue={userProfile?.emergencyContactNo || ''}
                  required
                />
                {userProfile?.emergencyContactNo && (
                  <p className="text-xs text-muted-foreground">
                    Auto-filled from your profile
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="emergencyContactRelationship">Relationship to Emergency Contact</Label>
                <select
                  id="emergencyContactRelationship"
                  name="emergencyContactRelationship"
                  className="w-full p-2 border border-border rounded-md bg-background text-foreground"
                  required
                  defaultValue={userProfile?.emergencyContactRelationship || ''}
                >
                  <option value="">Select relationship</option>
                  <option value="Parent">Parent</option>
                  <option value="Spouse">Spouse</option>
                  <option value="Sibling">Sibling</option>
                  <option value="Child">Child</option>
                  <option value="Friend">Friend</option>
                  <option value="Other">Other</option>
                </select>
                {userProfile?.emergencyContactRelationship && (
                  <p className="text-xs text-muted-foreground">
                    Auto-filled from your profile
                  </p>
                )}
              </div>

              <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-950/30 rounded-md border border-blue-100 dark:border-blue-900">
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  This information will only be used in case of emergency during the event. It will also be saved to your profile for future registrations.
                </p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* T-shirt selection if enabled */}
        {event.tshirtOptions?.enabled && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-shirt">
                  <path d="M20.38 3.46 16 2a4 4 0 0 1-8 0L3.62 3.46a2 2 0 0 0-1.34 2.23l.58 3.47a1 1 0 0 0 .99.84H6v10c0 1.1.9 2 2 2h8a2 2 0 0 0 2-2V10h2.15a1 1 0 0 0 .99-.84l.58-3.47a2 2 0 0 0-1.34-2.23z" />
                </svg>
                T-Shirt Selection
              </CardTitle>
              <CardDescription>
                Please select your preferred T-shirt size
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {event.tshirtOptions.description && (
                <p className="text-sm text-muted-foreground mb-4">{event.tshirtOptions.description}</p>
              )}

              <div className="space-y-2">
                <Label htmlFor="tshirtSize">T-Shirt Size</Label>
                <select
                  id="tshirtSize"
                  name="tshirtSize"
                  className="w-full p-2 border border-border rounded-md bg-background text-foreground"
                  required
                  defaultValue={userProfile?.tshirt_size || ''}
                >
                  <option value="">Select a size</option>
                  {event.tshirtOptions.sizes?.map((size: string) => (
                    <option key={size} value={size}>{size}</option>
                  ))}
                </select>
                {userProfile?.tshirt_size && (
                  <p className="text-xs text-muted-foreground">
                    Auto-filled from your profile
                  </p>
                )}
              </div>

              {event.tshirtOptions.sizeChartImage && (
                <div className="mt-4">
                  <p className="text-sm font-medium mb-2">Size Chart Reference</p>
                  <div className="relative w-full rounded-md overflow-hidden border border-border">
                    <a
                      href={event.tshirtOptions.sizeChartImage.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="block cursor-zoom-in"
                    >
                      <div className="relative w-full h-auto aspect-auto min-h-[400px]">
                        <img
                          src={event.tshirtOptions.sizeChartImage.url}
                          alt="T-shirt Size Chart"
                          className="object-contain w-full h-full"
                          style={{ maxWidth: '100%', maxHeight: '100%' }}
                        />
                        <div className="absolute inset-0 bg-black/5 hover:bg-black/10 transition-colors flex items-center justify-center opacity-0 hover:opacity-100">
                          <div className="bg-black/60 text-white px-4 py-2 rounded-full flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path><path d="M11 8v6"></path><path d="M8 11h6"></path></svg>
                            View Full Size
                          </div>
                        </div>
                      </div>
                    </a>
                  </div>
                  <p className="text-xs text-muted-foreground mt-2 text-center">
                    Click on the image to view full size
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Add any category-specific fields here */}
        {selectedCategory.properties?.customFields && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Additional Information</CardTitle>
              <CardDescription>
                Please provide the following additional information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Render custom fields based on category configuration */}
              <p className="text-sm text-muted-foreground">
                No additional information required for this category.
              </p>
            </CardContent>
          </Card>
        )}

        <Card>
          <CardHeader>
            <CardTitle>Confirm Registration</CardTitle>
            <CardDescription>
              Please review your information before submitting
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm mb-4">
              By registering for this event, you agree to the terms and conditions set by the event organizer.
            </p>

            <div className="bg-gradient-to-r from-indigo-50 to-indigo-100 dark:from-indigo-950/30 dark:to-indigo-900/20 p-5 rounded-lg mb-6 border border-indigo-200 dark:border-indigo-800">
              <h3 className="font-semibold text-lg mb-4 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-indigo-600 dark:text-indigo-400 mr-2"><path d="M22 12h-4l-3 9L9 3l-3 9H2" /></svg>
                Registration Summary
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-start">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-indigo-600 dark:text-indigo-400 mr-2 mt-0.5"><path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z" /><path d="M3 9V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v4" /><path d="M13 13h4" /><path d="M13 17h4" /><path d="M7 13h2" /><path d="M7 17h2" /></svg>
                    <div>
                      <div className="text-sm font-medium text-muted-foreground">Event</div>
                      <div className="font-medium">{event.title}</div>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <Tag className="text-indigo-600 dark:text-indigo-400 mr-2 mt-0.5 h-[18px] w-[18px]" />
                    <div>
                      <div className="text-sm font-medium text-muted-foreground">Category</div>
                      <div className="font-medium">{selectedCategory.name}</div>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-start">
                    <DollarSign className="text-indigo-600 dark:text-indigo-400 mr-2 mt-0.5 h-[18px] w-[18px]" />
                    <div>
                      <div className="text-sm font-medium text-muted-foreground">Price</div>
                      <div className="font-medium">
                        {((): React.ReactNode => {
                          const priceProp = selectedCategory.properties?.price;
                          const priceString = typeof priceProp === 'number' ? String(priceProp) : (typeof priceProp === 'string' ? priceProp : null);
                          if (priceString) {
                            return formatCurrency(parseFloat(priceString), event.country);
                          }
                          return 'Free Entry';
                        })()}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <Calendar className="text-indigo-600 dark:text-indigo-400 mr-2 mt-0.5 h-[18px] w-[18px]" />
                    <div>
                      <div className="text-sm font-medium text-muted-foreground">Date</div>
                      <div className="font-medium">
                        {formatDate(event.startDate)}
                        {event.startDate !== event.endDate && ` - ${formatDate(event.endDate)}`}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {event.tshirtOptions?.enabled && (
                <div className="mt-4 pt-4 border-t border-indigo-200 dark:border-indigo-800/50 flex items-start">
                  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-indigo-600 dark:text-indigo-400 mr-2 mt-0.5"><path d="M20.38 3.46 16 2a4 4 0 0 1-8 0L3.62 3.46a2 2 0 0 0-1.34 2.23l.58 3.47a1 1 0 0 0 .99.84H6v10c0 1.1.9 2 2 2h8a2 2 0 0 0 2-2V10h2.15a1 1 0 0 0 .99-.84l.58-3.47a2 2 0 0 0-1.34-2.23z" /></svg>
                  <div>
                    <div className="text-sm font-medium text-muted-foreground">T-Shirt</div>
                    <div className="font-medium">Included (select size during registration)</div>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
          <CardFooter className="flex flex-col sm:flex-row justify-between gap-4">
            <Button variant="outline" size="lg" className="w-full sm:w-auto" asChild>
              <Link href={`/events/${slug}/register`}>
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Categories
              </Link>
            </Button>
            <Button
              type="submit"
              size="lg"
              className="w-full sm:w-auto bg-indigo-600 hover:bg-indigo-700 text-white"
            >
              Complete Registration
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </CardFooter>
        </Card>
      </form>
    </div>
  );
}
