import { v4 as uuidv4 } from 'uuid';
import {
    FieldType,
    type EventField,
    type <PERSON>Field,
    type <PERSON><PERSON>ield,
    type SelectField,
    type <PERSON>SelectField,
    type CheckboxField,
    type DateField,
    type FileField,
    type DisplayField,
    type FieldOption
} from '@/types/event/field-types';

export class FieldFactory {
    static createField(type: FieldType, params: Partial<EventField> = {}): EventField {
        const baseField = {
            id: params.id || uuidv4(),
            type,
            label: params.label || '',
            description: params.description || null,
            placeholder: params.placeholder || null,
            required: params.required || false,
            order: params.order || 0
        };

        switch (type) {
            case FieldType.TEXT:
            case FieldType.TEXTAREA:
            case FieldType.EMAIL:
            case FieldType.PHONE:
                return {
                    ...baseField,
                    validationRules: null
                } as TextField;

            case FieldType.NUMBER:
                return {
                    ...baseField,
                    validationRules: null
                } as NumberField;

            case FieldType.SELECT:
            case FieldType.RADIO:
                return {
                    ...baseField,
                    options: [],
                    validationRules: null
                } as SelectField;

            case FieldType.MULTISELECT:
                return {
                    ...baseField,
                    options: [],
                    validationRules: null
                } as MultiSelectField;

            case FieldType.CHECKBOX:
                return {
                    ...baseField,
                    validationRules: null
                } as CheckboxField;

            case FieldType.DATE:
            case FieldType.TIME:
            case FieldType.DATETIME:
                return {
                    ...baseField,
                    validationRules: null
                } as DateField;

            case FieldType.FILE:
            case FieldType.IMAGE:
                return {
                    ...baseField,
                    validationRules: null
                } as FileField;

            case FieldType.DISPLAY:
                return {
                    ...baseField,
                    content: '',
                    validationRules: null
                } as DisplayField;

            default:
                throw new Error(`Unsupported field type: ${type}`);
        }
    }
} 