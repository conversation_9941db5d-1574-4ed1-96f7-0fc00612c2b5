import { Suspense } from 'react';
import { EventCategory, MalaysiaState } from '../../types/event';
import EventSearch from '../../components/EventSearch';
import EventResults from '../../components/EventResults';

export default async function EventsPage({
  searchParams
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  // Await the searchParams promise
  const resolvedSearchParams = await searchParams;

  // Convert searchParams to the expected format
  const parsedSearchParams = {
    keyword: typeof resolvedSearchParams.keyword === 'string' ? resolvedSearchParams.keyword : undefined,
    category: typeof resolvedSearchParams.category === 'string' ? resolvedSearchParams.category : undefined,
    state: typeof resolvedSearchParams.state === 'string' ? resolvedSearchParams.state : undefined
  };

  // Parse category and state to enums if they exist
  const category = parsedSearchParams.category as EventCategory | undefined;
  const state = parsedSearchParams.state as MalaysiaState | undefined;

  return (
    <main className="min-h-screen pb-16 pt-8">
      <div className="container max-w-5xl mx-auto px-4">
        <h1 className="text-3xl font-bold mb-8">Events</h1>

        <EventSearch className="mb-8" />

        <h2 className="text-xl font-semibold mb-6">
          {parsedSearchParams.keyword || category || state ? 'Search Results' : 'All Events'}
        </h2>

        <Suspense fallback={<div className="py-16 text-center">Loading events...</div>}>
          <EventResults
            keyword={parsedSearchParams.keyword || ""}
            category={category || null}
            state={state || null} />
        </Suspense>
      </div>
    </main>
  );
}