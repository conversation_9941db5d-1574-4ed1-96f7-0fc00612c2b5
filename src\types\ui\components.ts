/**
 * UI component type definitions
 * @module types/ui/components
 */

import { ReactNode } from 'react';
import * as ToastPrimitives from "@radix-ui/react-toast";
import { VariantProps } from "class-variance-authority";

/**
 * Base component props
 */
export interface BaseComponentProps {
    /** Optional CSS class names */
    className?: string;
    /** Optional component ID */
    id?: string;
    /** Optional child elements */
    children?: ReactNode;
}

/**
 * Badge component props
 */
export interface BadgeProps extends BaseComponentProps {
    /** Badge variant style */
    variant?: 'default' | 'secondary' | 'destructive' | 'outline';
}

/**
 * Button component props
 */
export interface ButtonProps extends BaseComponentProps {
    /** Button variant */
    variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'link';
    /** Button size */
    size?: 'sm' | 'md' | 'lg';
    /** Whether the button is disabled */
    disabled?: boolean;
    /** Whether the button is in loading state */
    loading?: boolean;
    /** Button type */
    type?: 'button' | 'submit' | 'reset';
    /** Click event handler */
    onClick?: () => void;
}

/**
 * Input component props
 */
export interface InputProps extends BaseComponentProps {
    /** Input name */
    name: string;
    /** Input label */
    label?: string;
    /** Input type */
    type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url';
    /** Input placeholder */
    placeholder?: string;
    /** Whether the input is required */
    required?: boolean;
    /** Whether the input is disabled */
    disabled?: boolean;
    /** Whether the input is readonly */
    readOnly?: boolean;
    /** Input error message */
    error?: string;
    /** Input helper text */
    helperText?: string;
    /** Input value */
    value?: string | number;
    /** Default input value */
    defaultValue?: string | number;
    /** Change event handler */
    onChange?: (value: string) => void;
}

/**
 * Select component props
 */
export interface SelectProps extends BaseComponentProps {
    /** Select name */
    name: string;
    /** Select label */
    label?: string;
    /** Select options */
    options: Array<{ label: string; value: string | number }>;
    /** Whether the select is required */
    required?: boolean;
    /** Whether the select is disabled */
    disabled?: boolean;
    /** Whether the select is readonly */
    readOnly?: boolean;
    /** Select error message */
    error?: string;
    /** Select helper text */
    helperText?: string;
    /** Selected value */
    value?: string | number;
    /** Default selected value */
    defaultValue?: string | number;
    /** Change event handler */
    onChange?: (value: string) => void;
}

/**
 * Card component props
 */
export interface CardProps extends BaseComponentProps {
    /** Card title */
    title?: string;
    /** Card description */
    description?: string;
    /** Card image URL */
    imageUrl?: string;
    /** Card footer content */
    footer?: ReactNode;
    /** Whether the card is hoverable */
    hoverable?: boolean;
    /** Whether the card has border */
    bordered?: boolean;
}

/**
 * Modal component props
 */
export interface ModalProps extends BaseComponentProps {
    /** Whether the modal is open */
    isOpen: boolean;
    /** Modal title */
    title?: string;
    /** Modal description */
    description?: string;
    /** Close event handler */
    onClose: () => void;
    /** Whether to show close button */
    showCloseButton?: boolean;
    /** Whether to close on overlay click */
    closeOnOverlayClick?: boolean;
    /** Whether to close on escape key press */
    closeOnEscape?: boolean;
}

/**
 * Toast notification props used by the toast hook and context
 */
export interface ToastNotificationProps {
    /** Unique identifier */
    id?: string;
    /** Toast title */
    title?: string;
    /** Toast description */
    description?: string;
    /** Toast variant style */
    variant?: "default" | "destructive" | "success" | "warning";
    /** Duration in milliseconds */
    duration?: number;
    /** Optional action element */
    action?: ReactNode;
    /** Dismiss callback */
    onDismiss?: () => void;
}

/**
 * Toast context type for managing toast notifications
 */
export interface ToastContextType {
    /** Add a new toast */
    toast: (props: ToastNotificationProps) => { id: string; dismiss: () => void };
    /** Dismiss a specific toast */
    dismiss: (id: string) => void;
    /** Dismiss all toasts */
    dismissAll: () => void;
}

/**
 * Toast component props for the Radix UI Toast primitive
 */
export interface ToastComponentProps extends React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root>,
    VariantProps<(props?: { variant?: "default" | "destructive" | "success" | "warning"; } & { class?: string; }) => string> {
}

/**
 * Toast action element type
 */
export type ToastActionElement = React.ReactElement<typeof ToastPrimitives.Action>;

/**
 * Textarea component props
 */
export type TextareaProps = React.TextareaHTMLAttributes<HTMLTextAreaElement>;

/**
 * Table component props
 */
export interface TableProps<T> extends BaseComponentProps {
    /** Table data */
    data: T[];
    /** Table columns */
    columns: Array<{
        /** Column key */
        key: keyof T;
        /** Column header */
        header: string;
        /** Column width */
        width?: number | string;
        /** Cell render function */
        render?: (value: T[keyof T], row: T) => ReactNode;
    }>;
    /** Whether the table is loading */
    loading?: boolean;
    /** Whether the table is sortable */
    sortable?: boolean;
    /** Whether the table has pagination */
    pagination?: boolean;
    /** Number of rows per page */
    pageSize?: number;
    /** Current page number */
    currentPage?: number;
    /** Total number of rows */
    totalRows?: number;
    /** Page change event handler */
    onPageChange?: (page: number) => void;
}

/**
 * Page header component props
 */
export interface PageHeaderProps extends BaseComponentProps {
    /** Page title */
    title: string;
    /** Optional page description */
    description?: string;
    /** Optional action buttons/elements */
    actions?: ReactNode;
} 