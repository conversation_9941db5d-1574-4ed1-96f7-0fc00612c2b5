/**
 * Event category type definitions
 * @module types/event/category
 */

/**
 * Base event category properties
 */
export interface BaseEventCategory<T = Record<string, unknown>> {
    /** Unique identifier for the category */
    id: string;
    /** Associated event ID */
    eventId: string;
    /** Category name */
    name: string;
    /** Optional category description */
    description?: string;
    /** Additional category properties */
    properties: T;
    /** Creation timestamp */
    createdAt: Date;
    /** Last update timestamp */
    updatedAt: Date;
}

/**
 * Running event category properties
 */
export interface RunningEventCategoryProperties {
    /** Distance in kilometers */
    distance?: number;
    /** Registration price */
    price?: number;
    /** Start time */
    startTime?: string;
    /** Early bird registration price */
    earlyBirdPrice?: number;
    /** Early bird registration end date */
    earlyBirdEndDate?: string;
    /** Bib number prefix */
    bibPrefix?: string;
    /** Starting bib number */
    bibStartNumber?: string;
    /** Whether bib numbers need to be generated */
    bibRequireGeneration?: boolean;
    /** Whether registration is open */
    registrationOpen?: boolean;
    /** Registration close date */
    registrationCloseDate?: string;
    /** Maximum number of registrations */
    registrationLimit?: number;
    /** Current number of registrations */
    registrationCount?: number;
    /** Additional dynamic properties */
    [key: string]: unknown;
}

/**
 * Conference event category properties
 */
export interface ConferenceEventCategoryProperties {
    /** Venue name */
    venue?: string;
    /** Speaker name */
    speaker?: string;
    /** Session duration in minutes */
    sessionDuration?: number;
    /** Registration price */
    price?: number;
    /** Start time */
    startTime?: string;
    /** End time */
    endTime?: string;
    /** Maximum number of registrations */
    registrationLimit?: number;
    /** Current number of registrations */
    registrationCount?: number;
    /** Additional dynamic properties */
    [key: string]: unknown;
}

/**
 * Running event category with specific properties
 */
export type RunningEventCategory = BaseEventCategory<RunningEventCategoryProperties>;

/**
 * Conference event category with specific properties
 */
export type ConferenceEventCategory = BaseEventCategory<ConferenceEventCategoryProperties>;

/**
 * Generic event category
 * Uses base category with generic properties
 */
export type EventCategory = BaseEventCategory; 