/**
 * Event schema validation
 * @module lib/validations/event-schema
 */

import { z } from 'zod';
import { FieldType, EventField } from '@/types/event-types';
import { eventFieldSchema } from '@/types/event/field-types';

/**
 * Event status enum
 */
export const eventStatusEnum = z.enum(['draft', 'published', 'cancelled', 'completed']);
export type EventStatus = z.infer<typeof eventStatusEnum>;

/**
 * Event form data schema
 */
export const eventSchema = z.object({
  /** Event ID */
  id: z.string().optional(),

  /** Event title */
  title: z.string().min(1, 'Title is required'),

  /** Event description */
  description: z.string().min(1, 'Description is required'),

  /** Event type ID */
  eventTypeId: z.string(),

  /** Event type */
  eventType: z.string(),

  /** Event start date */
  startDate: z.string(),

  /** Event end date */
  endDate: z.string(),

  /** Event location */
  location: z.string().min(1, 'Location is required'),

  /** Event country */
  country: z.string().optional(),

  /** Event state */
  state: z.string().optional(),

  /** Event city */
  city: z.string().optional(),

  /** Event timezone */
  timezone: z.string(),

  /** Event capacity */
  capacity: z.number().min(1, 'Capacity must be at least 1'),

  /** Total capacity */
  totalCapacity: z.number().optional(),

  /** Event registration deadline */
  registrationDeadline: z.string(),

  /** Registration close date */
  registrationCloseDate: z.string(),

  /** Event status */
  status: eventStatusEnum,

  /** Event categories */
  categories: z.array(z.any()),

  /** Event custom fields */
  customFields: z.array(eventFieldSchema),

  /** Allow category specific closing dates */
  allowCategorySpecificClosingDates: z.boolean(),

  /** Emergency contact settings */
  emergencyContactSettings: z.object({
    required: z.boolean(),
    fields: z.array(z.string()),
    allowSameForMultipleRegistrations: z.boolean(),
  }).optional(),

  /** T-shirt options */
  tshirtOptions: z.object({
    enabled: z.boolean().nullable(),
    sizes: z.array(z.string()).nullable(),
    description: z.string().nullable().optional(),
    sizeChartImage: z.any().nullable().optional(),
  }).optional(),

  /** Event images */
  posterImage: z.object({
    url: z.string(),
    path: z.string().optional(),
  }).nullable().optional(),

  coverImage: z.object({
    url: z.string(),
    path: z.string().optional(),
    focusPoint: z.object({
      x: z.number(),
      y: z.number(),
    }).optional(),
  }).nullable().optional(),

  galleryImages: z.array(z.object({
    url: z.string(),
    path: z.string().optional(),
  })).nullable().optional(),

  /** Registration settings */
  registrationSettings: z.object({
    requireApproval: z.boolean(),
    requirePayment: z.boolean(),
    allowTeamRegistration: z.boolean(),
    allowMultipleRegistrations: z.boolean(),
    allowWaitlist: z.boolean(),
    allowCancellation: z.boolean(),
    allowTransfer: z.boolean(),
    allowRefund: z.boolean(),
    maxTeamSize: z.number(),
    minTeamSize: z.number(),
  }).optional(),
});

/**
 * Event form data type
 */
export type FormData = z.infer<typeof eventSchema>;

/**
 * Schema for emergency contact settings
 */
export const emergencyContactSettingsSchema = z.object({
  required: z.boolean(),
  fields: z.array(z.string()),
  allowSameForMultipleRegistrations: z.boolean()
});

/**
 * Schema for t-shirt options
 */
export const tshirtOptionsSchema = z.object({
  enabled: z.boolean(),
  sizes: z.array(z.string()),
  description: z.string().nullable(),
  sizeChartImage: z.any().nullable()
});

/**
 * Schema for registration settings
 */
export const registrationSettingsSchema = z.object({
  allowMultiple: z.boolean(),
  maxRegistrationsPerUser: z.number(),
  requireApproval: z.boolean(),
  allowWaitlist: z.boolean(),
  waitlistLimit: z.number(),
  allowCancellation: z.boolean(),
  cancellationDeadline: z.string().nullable(),
  allowTransfer: z.boolean(),
  transferDeadline: z.string().nullable(),
  allowRefund: z.boolean(),
  refundDeadline: z.string().nullable(),
  refundPercentage: z.number()
});

/**
 * Schema for event data
 */
export const createEventSchema = z.object({
  id: z.string().optional(),
  title: z.string().min(1, 'Title is required'),
  description: z.string(),
  eventTypeId: z.string(),
  eventType: z.string(),
  location: z.string(),
  country: z.string(),
  state: z.string(),
  city: z.string(),
  startDate: z.string(),
  endDate: z.string(),
  timezone: z.string(),
  status: z.enum(['draft', 'published', 'cancelled', 'completed']),
  categories: z.array(z.any()),
  customFields: z.array(eventFieldSchema),
  totalCapacity: z.number(),
  registrationCloseDate: z.string(),
  allowCategorySpecificClosingDates: z.boolean(),
  tshirtOptions: tshirtOptionsSchema,
  emergencyContactSettings: emergencyContactSettingsSchema,
  registrationSettings: registrationSettingsSchema.optional()
});

/**
 * Type for form data
 */
export type FormData = z.infer<typeof createEventSchema>;

// Export other schemas
export const basicDetailsSchema = eventSchema.pick({
  title: true,
  description: true,
  location: true,
  country: true,
  state: true,
  city: true,
  startDate: true,
  endDate: true,
  timezone: true,
});

// Category validation
export const categorySchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, 'Category name is required'),
  description: z.string().optional(),
  price: z.number().nonnegative().optional(),
  startTime: z.string().optional(),
  earlyBirdPrice: z.number().nonnegative().optional(),
  earlyBirdEndDate: z.string().optional(),
  bibPrefix: z.string().optional(),
  bibStartNumber: z.union([
    z.string().optional(),
    z.number().int().nonnegative().optional()
  ]),
  bibRequireGeneration: z.boolean().default(true),
  registrationOpen: z.boolean().default(true),
  registrationCloseDate: z.string().optional(),
  registrationLimit: z.number().int().positive().optional(),
  customFields: z.array(z.any()).optional(),
  properties: z.record(z.any()).optional(),
});

// Image validation
export const imageSchema = z.object({
  url: z.string().url('Invalid image URL'),
  path: z.string(),
}).nullable();

// Emergency contact settings validation
export const emergencyContactSchema = z.object({
  required: z.boolean().default(false),
  fields: z.array(z.string()).default(['name', 'phone', 'relationship']),
  allowSameForMultipleRegistrations: z.boolean().default(true),
});

// T-shirt options validation
export const tshirtOptionsSchema = z.object({
  enabled: z.boolean().nullable().default(false),
  sizes: z.array(z.string()).nullable().default(["XS", "S", "M", "L", "XL", "XXL", "XXXL"]),
  description: z.string().nullable().optional(),
  sizeChartImage: z.any().nullable().optional(),
});
