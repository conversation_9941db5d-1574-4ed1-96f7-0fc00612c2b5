# Type Repository Migration Plan

## Overview
This document outlines the plan to consolidate and organize all TypeScript types into a centralized type repository structure. The goal is to improve code organization, reduce duplication, and maintain consistency across the codebase.

## Current Structure
```
src/types/
├── auth/
│   ├── roles.ts
│   └── index.ts
├── database/
│   ├── schema.ts
│   └── index.ts
├── event/
│   ├── field.ts
│   ├── category.ts
│   ├── registration.ts
│   └── index.ts
├── contacts/
│   ├── schema.ts
│   ├── api.ts
│   └── index.ts
├── ui/
│   ├── components.ts
│   └── index.ts
└── index.ts
```

## Tasks

### 1. Remove Duplicate Types
- [x] Delete `src/types/event-types.ts` (duplicates `src/types/event/field.ts`)
- [x] Remove duplicate contact types from components
- [x] Identify and remove other duplicate type definitions

### 2. Update Component Types
#### UI Components to Update:
- [x] `src/components/ui/badge.tsx`
- [x] `src/components/ui/toast.tsx`
- [x] `src/components/ui/textarea.tsx`
- [x] `src/components/page-header.tsx`
- [x] `src/components/ui/use-toast.tsx`

#### Feature Components to Update:
- [x] `src/components/profile/data-export.tsx`
- [x] `src/components/events/event-wizard/steps/fields-step.tsx`
- [x] `src/components/profile/contacts-list.tsx`

### 3. Update Type Imports
- [x] Update imports to use barrel files
- [x] Use `@/types` for root-level imports
- [x] Use `@/types/{auth,database,event,ui,contacts}` for domain-specific imports

### 4. Add Missing Types
- [x] Add `PageHeaderProps` to `src/types/ui/components.ts`
- [x] Add `ExportOption` and `ExportType` to appropriate domain file
- [x] Add field types to `src/types/event/field.ts`:
  - [x] `BaseField`
  - [x] `TextField`
  - [x] `NumberField`
  - [x] `SelectField`
  - [x] `DisplayField`
  - [x] `CheckboxField`
  - [x] `DateTimeField`
  - [x] `EventField` (union type)
- [x] Add contact types to `src/types/contacts/schema.ts`:
  - [x] `Contact`
  - [x] `ApiContact`
  - [x] `ContactFormData`
  - [x] `ContactRelationship`

### 5. Standardize Type Usage
- [x] Enforce PascalCase for interfaces and types
- [x] Enforce camelCase for properties
- [x] Add JSDoc documentation for all types
- [x] Standardize common prop patterns

### 6. Testing and Validation
- [x] Run TypeScript compiler checks
- [x] Test components with new type imports
- [x] Verify type exports

### 7. Contacts Type System Refactoring
#### Current Status
1. Schema Cache Issue
   - ✅ Emergency contact fields now recognized in Supabase schema cache
   - ✅ Migration applied successfully
   - ✅ Temporary workaround code removed

2. Type Inconsistencies
   - ✅ Consolidated schema definitions in `src/types/contacts/schema.ts`
   - ✅ Consistent validation rules implemented
   - ✅ Relationship field handling standardized

3. API Type Safety
   - ✅ Type assertions removed
   - ✅ Proper error type definitions added
   - ✅ Consistent error handling patterns implemented

4. Code Organization
   - ✅ API logic follows consistent patterns
   - ✅ Comprehensive logging added
   - ✅ Improved error messages
   - ✅ Test coverage added

5. Frontend Integration
   - ✅ Loading states added
   - ✅ Error handling UX improved
   - ✅ Form validation feedback added
   - ✅ Accessibility improvements implemented

## Progress Tracking

### Completed
- [x] Created type directory structure
- [x] Set up barrel files
- [x] Migrated core types
- [x] Removed duplicate types
- [x] Updated component imports
- [x] Added missing types
- [x] Standardized type usage
- [x] Added contact types and schemas
- [x] Implemented form validation patterns
- [x] Added proper error handling
- [x] Enhanced accessibility
- [x] Added loading states
- [x] Fixed all type safety issues

### In Progress
- [ ] Type versioning strategy
- [ ] Migration guide for new components
- [ ] Type testing and validation tools
- [ ] Documentation generation
- [ ] Type usage analytics

## Notes
- Keep types close to their domain
- Avoid circular dependencies
- Use generics for reusable types
- Break down large interfaces
- Document breaking changes

## Future Considerations
1. Type versioning strategy
2. Migration guide for new components
3. Type testing and validation tools
4. Documentation generation
5. Type usage analytics 