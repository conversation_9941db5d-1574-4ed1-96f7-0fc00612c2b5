"use client"
import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>T<PERSON><PERSON> } from "@/components/ui/card";
import { getUserAvatar } from "@/utils/imageHandling";
import Image from "next/image";
import { But<PERSON> } from "../ui/button";
import { ProfileEditForm } from "./edit-form";
import { toast } from "@/components/ui/use-toast";
import { Users, Mars, Venus, Edit } from "lucide-react";
import { createClient } from "@/lib/supabase/client";
import { logger } from '@/lib/logger';
import { supabase } from '@/lib/supabase/client';
import { updateProfile, FormData } from '@/lib/api';

// Make sure this type matches the one in edit-form.tsx
type UserProfile = {
  id: string;
  first_name: string;
  last_name?: string;
  username?: string;
  gender?: string;
  bio?: string;
  avatar?: string;
  isPublic: number;
  eventCategories?: string | null;
  nationality?: string;
  ic?: string;
  passport?: string;
  dateOfBirth?: string;
  contactNo?: string;
  address?: string;
  apartment?: string;
  city?: string;
  postcode?: string;
  country?: string;
  state?: string;
  emergencyContactName?: string;
  emergencyContactNo?: string;
  emergencyContactRelationship?: string;
  tshirtSize?: string;
  tshirt_size?: string;
  stats?: {
    eventsAttended?: number;
    eventsHosted?: number;
    categories?: string[];
  };
};

interface PersonalInfoProps {
  isEditing?: boolean;
  onToggleEdit?: () => void;
}

export function PersonalInfo({ isEditing: externalIsEditing, onToggleEdit }: PersonalInfoProps = {}) {
  const supabaseClient = createClient();
  const [internalIsEditing, setInternalIsEditing] = useState(false);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshFlag, setRefreshFlag] = useState(0);
  const [categories, setCategories] = useState<string[]>([]);

  // Use external editing state if provided, otherwise use internal
  const isEditing = externalIsEditing !== undefined ? externalIsEditing : internalIsEditing;
  const setIsEditing = (value: boolean) => {
    if (onToggleEdit && externalIsEditing !== undefined) {
      // If external control is provided, use it
      onToggleEdit();
    } else {
      // Otherwise use internal state
      setInternalIsEditing(value);
    }
  };

  // Function to fetch user profile
  const fetchProfile = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get the current authenticated user
      const { data: { user: authUser }, error: authError } = await supabaseClient.auth.getUser();

      if (authError || !authUser) {
        console.error('Authentication error:', authError);
        throw new Error('Authentication failed. Please sign in again.');
      }

      // Get the session to include the access token
      const { data: { session } } = await supabaseClient.auth.getSession();

      if (!session) {
        console.error('No active session found');
        throw new Error('No active session. Please sign in again.');
      }

      // Try to fetch directly from Supabase first
      try {
        const { data: userData, error: userError } = await supabaseClient
          .from('users')
          .select('*')
          .eq('auth_user_id', authUser.id)
          .single();

        if (!userError && userData) {
          // Parse event categories if exists
          let parsedCategories: string[] = [];
          if (userData.eventCategories) {
            try {
              parsedCategories = typeof userData.eventCategories === 'string'
                ? JSON.parse(userData.eventCategories)
                : userData.eventCategories;
            } catch (e) {
              console.error('Error parsing eventCategories:', e);
            }
          }

          // Ensure stats are properly structured
          const profileData: UserProfile = {
            id: userData.id,
            first_name: userData.first_name,
            last_name: userData.last_name || '',
            username: userData.username || '',
            gender: userData.gender || '',
            bio: userData.bio || '',
            avatar: userData.avatar || '',
            isPublic: userData.isPublic || 0,
            eventCategories: userData.eventCategories || null,
            nationality: userData.nationality || '',
            ic: userData.ic || '',
            passport: userData.passport || '',
            dateOfBirth: userData.dateOfBirth || '',
            contactNo: userData.contactNo || '',
            address: userData.address || '',
            apartment: userData.apartment || '',
            city: userData.city || '',
            postcode: userData.postcode || '',
            country: userData.country || '',
            state: userData.state || '',
            emergencyContactName: userData.emergencyContactName || '',
            emergencyContactNo: userData.emergencyContactNo || '',
            emergencyContactRelationship: userData.emergencyContactRelationship || '',
            tshirtSize: userData.tshirt_size || '',
            tshirt_size: userData.tshirt_size || '',
            stats: {
              eventsAttended: 0,
              eventsHosted: 0,
              categories: parsedCategories
            }
          };

          setProfile(profileData);
          return;
        }
      } catch (directError) {
        console.error('Error fetching directly from Supabase:', directError);
        // Continue to API fallback
      }

      // Fallback to API if direct fetch fails
      logger.info('Falling back to API fetch for profile data');
      const response = await fetch('/api/user/profile', {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        cache: 'no-store',
        next: { revalidate: 0 },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Failed to fetch profile: ${response.status}`, errorText);
        throw new Error(`Failed to fetch profile: ${response.status}`);
      }

      const data = await response.json();

      // Parse event categories if exists
      let parsedCategories: string[] = [];
      if (data.eventCategories) {
        try {
          parsedCategories = typeof data.eventCategories === 'string'
            ? JSON.parse(data.eventCategories)
            : data.eventCategories;
        } catch (e) {
          console.error('Error parsing eventCategories:', e);
        }
      }

      // Ensure stats are properly structured
      const apiProfileData: UserProfile = {
        ...data,
        stats: {
          eventsAttended: data.stats?.eventsAttended || 0,
          eventsHosted: data.stats?.eventsHosted || 0,
          categories: parsedCategories
        }
      };

      setProfile(apiProfileData);
    } catch (error) {
      console.error('Error fetching profile:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch profile');
    } finally {
      setLoading(false);
    }
  };

  // Fetch profile when component mounts or refreshFlag changes
  useEffect(() => {
    fetchProfile();
  }, [refreshFlag]);

  // Fetch categories
  const fetchCategories = async () => {
    try {
      const { data: eventCategories, error } = await supabaseClient
        .from('event_categories')
        .select('name');

      if (error) {
        throw error;
      }

      if (eventCategories) {
        setCategories(eventCategories.map(cat => cat.name));
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  // Handle form submission
  const handleSubmit = async () => {
    try {
      if (!profile) return;

      setLoading(true);
      setError(null);

      // Convert UserProfile to FormData
      const apiFormData: FormData = {
        first_name: profile.first_name,
        ...(profile.last_name && { last_name: profile.last_name }),
        ...(profile.username && { username: profile.username }),
        ...(profile.gender && { gender: profile.gender }),
        ...(profile.bio && { bio: profile.bio }),
        ...(profile.avatar && { avatar: profile.avatar }),
        isPublic: profile.isPublic,
        eventCategories: profile.stats?.categories || [],
        ...(profile.nationality && { nationality: profile.nationality }),
        ...(profile.ic && { ic: profile.ic }),
        ...(profile.passport && { passport: profile.passport }),
        ...(profile.dateOfBirth && { dateOfBirth: profile.dateOfBirth }),
        ...(profile.contactNo && { contactNo: profile.contactNo }),
        ...(profile.address && { address: profile.address }),
        ...(profile.apartment && { apartment: profile.apartment }),
        ...(profile.city && { city: profile.city }),
        ...(profile.postcode && { postcode: profile.postcode }),
        ...(profile.country && { country: profile.country }),
        ...(profile.state && { state: profile.state }),
        ...(profile.emergencyContactName && { emergencyContactName: profile.emergencyContactName }),
        ...(profile.emergencyContactNo && { emergencyContactNo: profile.emergencyContactNo }),
        ...(profile.emergencyContactRelationship && { emergencyContactRelationship: profile.emergencyContactRelationship }),
        ...(profile.tshirt_size && { tshirtSize: profile.tshirt_size })
      };

      // Update profile
      await updateProfile(apiFormData);

      // Update categories
      setCategories(profile.stats?.categories || []);

      // Refresh profile data
      setRefreshFlag((prev: number) => prev + 1);

      // Exit edit mode
      setIsEditing(false);

      toast({
        title: "Success",
        description: "Profile updated successfully",
      });
    } catch (error) {
      console.error('Error updating profile:', error);
      setError(error instanceof Error ? error.message : 'Failed to update profile');
      toast({
        title: "Error",
        description: "Failed to update profile",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error && !profile) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-500">{error}</div>
        </CardContent>
      </Card>
    );
  }

  if (isEditing && profile) {
    return (
      <ProfileEditForm
        profile={profile}
        onSuccess={handleSubmit}
        onCancel={() => setIsEditing(false)}
      />
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-2xl font-bold">Personal Information</CardTitle>
        <Button
          onClick={() => setIsEditing(true)}
          variant="outline"
          size="icon"
          className="h-8 w-8"
        >
          <Edit className="h-4 w-4" />
        </Button>
      </CardHeader>
      <CardContent className="grid gap-4">
        {/* Profile content */}
      </CardContent>
    </Card>
  );
}
