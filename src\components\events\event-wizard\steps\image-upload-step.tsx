'use client';

import { useState, useEffect, useRef } from 'react';
import { useWizard } from '@/components/events/event-wizard/wizard-container';
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { Upload, X, ImagePlus, Loader2, Info, Plus, MoveVertical } from 'lucide-react';
import Image from 'next/image';
import { createClient } from '@/lib/supabase/client';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { ImageFocusAdjuster } from '@/components/events/image-focus-adjuster';
import { logger } from '@/lib/logger';

// Define image types
type ImageType = 'poster' | 'cover' | 'gallery';

// Define image object structure
interface ImageObject {
  url: string;
  path: string;
  focusPoint?: {
    y: number;
  };
}

interface WizardContextType<T = unknown> {
  formData: T;
  updateFormData: (data: Partial<T>) => void;
  nextStep: () => void;
}

interface WizardFormData {
  id?: string;
  coverImage?: ImageObject;
  posterImage?: ImageObject;
  galleryImages?: ImageObject[];
}

export function ImageUploadStep() {
  const { formData, updateFormData, nextStep } = useWizard() as WizardContextType<WizardFormData>;
  const [isUploading, setIsUploading] = useState<ImageType | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [galleryImages, setGalleryImages] = useState<ImageObject[]>(formData.galleryImages || []);
  const [isGalleryUploading, setIsGalleryUploading] = useState(false);
  const [isFocusAdjusterOpen, setIsFocusAdjusterOpen] = useState(false);

  // Drag and drop states
  const [isDraggingCover, setIsDraggingCover] = useState(false);
  const [isDraggingPoster, setIsDraggingPoster] = useState(false);
  const [isDraggingGallery, setIsDraggingGallery] = useState(false);

  // Refs for file inputs
  const coverInputRef = useRef<HTMLInputElement>(null);
  const posterInputRef = useRef<HTMLInputElement>(null);
  const galleryInputRef = useRef<HTMLInputElement>(null);

  // Maximum number of gallery images allowed
  const MAX_GALLERY_IMAGES = 10;

  // Initialize Supabase client
  const supabase = createClient();

  // Check authentication status on component mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        if (process.env.NODE_ENV === 'development') {

          logger.debug('[DEBUG] ImageUploadStep - Auth session check:', session ? 'Authenticated' : 'Not authenticated');

        }
        if (session?.user) {
          if (process.env.NODE_ENV === 'development') {

            logger.debug('[DEBUG] ImageUploadStep - User ID:', session.user.id);

          }
        } else {
          if (process.env.NODE_ENV === 'development') {

            logger.debug('[DEBUG] ImageUploadStep - No session found, but will continue without redirecting');

          }
          // Don't show error - middleware should handle authentication
        }
      } catch (error) {
        console.error('[DEBUG] ImageUploadStep - Error checking auth:', error);
        // Don't redirect or show error - just log it
      }
    };

    checkAuth();
  }, []);

  // Override next button to proceed without validation
  useEffect(() => {
    const nextButton = document.getElementById('wizard-next-button') as HTMLButtonElement;
    if (nextButton) {
      nextButton.onclick = () => nextStep();
    }
  }, [nextStep]);

  // Drag and drop handlers
  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>, type: ImageType) => {
    e.preventDefault();
    e.stopPropagation();

    switch (type) {
      case 'cover':
        setIsDraggingCover(true);
        break;
      case 'poster':
        setIsDraggingPoster(true);
        break;
      case 'gallery':
        setIsDraggingGallery(true);
        break;
    }
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>, type: ImageType) => {
    e.preventDefault();
    e.stopPropagation();

    // Only set isDragging to false if we're leaving the dropzone itself, not its children
    if (e.currentTarget === e.target) {
      switch (type) {
        case 'cover':
          setIsDraggingCover(false);
          break;
        case 'poster':
          setIsDraggingPoster(false);
          break;
        case 'gallery':
          setIsDraggingGallery(false);
          break;
      }
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>, type: ImageType) => {
    e.preventDefault();
    e.stopPropagation();

    // Reset all dragging states
    setIsDraggingCover(false);
    setIsDraggingPoster(false);
    setIsDraggingGallery(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      if (type === 'gallery') {
        handleGalleryUpload(e.dataTransfer.files);
      } else if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
        handleSingleFileUpload(e.dataTransfer.files[0], type);
      }
    }
  };

  // Trigger file input click
  const triggerFileInput = (type: ImageType) => {
    switch (type) {
      case 'cover':
        if (coverInputRef.current) coverInputRef.current.click();
        break;
      case 'poster':
        if (posterInputRef.current) posterInputRef.current.click();
        break;
      case 'gallery':
        if (galleryInputRef.current) galleryInputRef.current.click();
        break;
    }
  };

  // Handle file upload for poster and cover images
  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>, type: ImageType) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    // For gallery images, handle multiple files
    if (type === 'gallery') {
      await handleGalleryUpload(files);
      return;
    }

    // For poster and cover, handle single file
    const file = files[0];
    await handleSingleFileUpload(file, type);
  };

  // Handle single file upload (poster or cover)
  const handleSingleFileUpload = async (file: File | undefined, type: ImageType) => {
    if (!file) {
      toast({
        title: "Upload Failed",
        description: "No file selected",
        variant: "destructive",
      });
      return;
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid File Type",
        description: "Please upload an image file",
        variant: "destructive",
      });
      return;
    }

    // Validate file size (5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
      toast({
        title: "File Too Large",
        description: "Image must be less than 5MB",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsUploading(type);
      setError(null);

      // Create a unique file path
      const fileExt = file.name.split('.').pop();
      const fileName = `${type}-${Date.now()}.${fileExt}`;
      const filePath = `events/${formData.id || 'temp'}/${fileName}`;

      // Upload file to Supabase Storage
      const { error: uploadError, data } = await supabase.storage
        .from('event-images')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false,
        });

      if (uploadError) {
        throw new Error(uploadError.message);
      }

      // Get public URL for the uploaded file
      const { data: { publicUrl } } = supabase.storage
        .from('event-images')
        .getPublicUrl(filePath);

      // Update form data with the new image
      const imageObject: ImageObject = {
        url: publicUrl,
        path: filePath,
      };

      if (type === 'cover') {
        updateFormData({ coverImage: imageObject });
      } else if (type === 'poster') {
        updateFormData({ posterImage: imageObject });
      }

      toast({
        title: "Upload Successful",
        description: "Image has been uploaded successfully",
      });
    } catch (error) {
      console.error('Upload error:', error);
      setError(error instanceof Error ? error.message : 'An error occurred during upload');
      toast({
        title: "Upload Failed",
        description: error instanceof Error ? error.message : 'An error occurred during upload',
        variant: "destructive",
      });
    } finally {
      setIsUploading(null);
      setUploadProgress(0);
    }
  };

  // Handle gallery image uploads (multiple files)
  const handleGalleryUpload = async (files: FileList) => {
    if (!files.length) {
      toast({
        title: "Upload Failed",
        description: "No files selected",
        variant: "destructive",
      });
      return;
    }

    // Check if adding these files would exceed the maximum
    if (galleryImages.length + files.length > MAX_GALLERY_IMAGES) {
      toast({
        title: "Too Many Images",
        description: `You can only upload up to ${MAX_GALLERY_IMAGES} images in total`,
        variant: "destructive",
      });
      return;
    }

    setIsGalleryUploading(true);
    setError(null);

    const newImages: ImageObject[] = [];
    for (let i = 0; i < files.length; i++) {
      const file = files[i];

      // Skip if file is undefined
      if (!file) continue;

      // Validate file type
      if (!file.type.startsWith('image/')) continue;

      // Validate file size
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) continue;

      try {
        // Create a unique file path
        const fileExt = file.name.split('.').pop() || 'jpg';
        const fileName = `gallery-${Date.now()}-${i}.${fileExt}`;
        const filePath = `events/${formData.id || 'temp'}/gallery/${fileName}`;

        // Upload file to Supabase Storage
        const { error: uploadError } = await supabase.storage
          .from('event-images')
          .upload(filePath, file, {
            cacheControl: '3600',
            upsert: false,
          });

        if (uploadError) {
          console.error(`Error uploading gallery image ${i + 1}/${files.length}:`, uploadError);
          continue;
        }

        // Get public URL
        const { data } = supabase.storage
          .from('event-images')
          .getPublicUrl(filePath);

        if (data?.publicUrl) {
          newImages.push({
            url: data.publicUrl,
            path: filePath,
          });
        }
      } catch (err) {
        console.error(`Error uploading gallery image ${i + 1}/${files.length}:`, err);
      }
    }

    // Update gallery images
    if (newImages.length > 0) {
      const updatedGalleryImages = [...galleryImages, ...newImages];
      setGalleryImages(updatedGalleryImages);
      updateFormData({ galleryImages: updatedGalleryImages });

      toast({
        title: "Upload Successful",
        description: `Successfully uploaded ${newImages.length} image${newImages.length !== 1 ? 's' : ''}`,
      });
    } else {
      toast({
        title: "Upload Failed",
        description: "Failed to upload any images",
        variant: "destructive",
      });
    }

    setIsGalleryUploading(false);
  };

  // Remove an uploaded image (poster or cover)
  const handleRemoveImage = (type: 'poster' | 'cover') => {
    // If there's a path, delete from storage
    if (formData[`${type}Image`]?.path) {
      logger.info(`Removing ${type} image from storage:`, formData[`${type}Image`].path);
      supabase
        .storage
        .from('images')
        .remove([formData[`${type}Image`].path])
        .then(({ data, error }) => {
          if (error) {
            console.error(`Error removing ${type} image:`, error);
          } else {
            logger.info(`Successfully removed ${type} image:`, data);
          }
        });
    }

    // Update form data to remove the image
    updateFormData({
      [`${type}Image`]: undefined
    });
  };

  // Handle opening the focus adjuster
  const handleOpenFocusAdjuster = () => {
    setIsFocusAdjusterOpen(true);
  };

  // Handle saving focus point
  const handleSaveFocusPoint = (focusPoint: { y: number }) => {
    if (formData.coverImage) {
      // Update the cover image with the new focus point
      updateFormData({
        coverImage: {
          ...formData.coverImage,
          focusPoint
        }
      });

      toast({
        title: "Vertical position saved",
        description: "Your cover image vertical position has been adjusted successfully.",
      });
    }
  };

  // Remove a gallery image
  const handleRemoveGalleryImage = (index: number) => {
    // Get the image to remove
    const imageToRemove = galleryImages[index];

    // Remove from storage
    if (imageToRemove?.path) {
      logger.info(`Removing gallery image from storage:`, imageToRemove.path);
      supabase
        .storage
        .from('images')
        .remove([imageToRemove.path])
        .then(({ data, error }) => {
          if (error) {
            console.error(`Error removing gallery image:`, error);
          } else {
            logger.info(`Successfully removed gallery image:`, data);
          }
        });
    }

    // Update state
    const updatedGalleryImages = [...galleryImages];
    updatedGalleryImages.splice(index, 1);
    setGalleryImages(updatedGalleryImages);

    // Update form data
    updateFormData({
      galleryImages: updatedGalleryImages
    });
  };

  return (
    <div className="space-y-6">

      <div>
        <h2 className="text-2xl font-semibold mb-2">Event Images</h2>
        <p className="text-gray-500">
          Upload images for your event (optional)
        </p>
      </div>

      {/* Cover Image Upload */}
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Label htmlFor="coverImage" className="text-lg font-medium">
                  Cover Image
                </Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-4 w-4 text-gray-400" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">
                        This image will appear as a banner at the top of your event page.
                        Recommended size: 1200×400 pixels.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>

              {formData.coverImage && (
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleOpenFocusAdjuster}>
                    <MoveVertical className="h-4 w-4 mr-1" />
                    Adjust Vertical Position
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleRemoveImage('cover')}
                    className="text-red-500"
                  >
                    <X className="h-4 w-4 mr-1" />
                    Remove
                  </Button>
                </div>
              )}
            </div>

            {formData.coverImage ? (
              <div className="relative w-full h-[200px] rounded-md overflow-hidden border">
                <Image
                  src={formData.coverImage.url}
                  alt="Cover Image"
                  fill
                  className="object-cover"
                  style={formData.coverImage.focusPoint ? {
                    objectPosition: `center ${formData.coverImage.focusPoint.y}%`
                  } : {}} />
              </div>
            ) : (
              <div
                className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${isDraggingCover
                  ? 'border-primary bg-primary/5'
                  : 'border-gray-300 hover:border-primary/50'
                  }`}
                onDragEnter={(e) => handleDragEnter(e, 'cover')}
                onDragOver={handleDragOver}
                onDragLeave={(e) => handleDragLeave(e, 'cover')}
                onDrop={(e) => handleDrop(e, 'cover')}
              >
                <input
                  type="file"
                  id="coverImage"
                  accept="image/*"
                  onChange={(e) => handleFileUpload(e, 'cover')}
                  className="hidden"
                  disabled={!!isUploading}
                  ref={coverInputRef}
                />
                <div
                  onClick={() => triggerFileInput('cover')}
                  className="flex flex-col items-center justify-center cursor-pointer"
                >
                  {isUploading === 'cover' ? (
                    <div className="flex flex-col items-center space-y-2">
                      <Loader2 className="w-8 h-8 text-primary animate-spin" />
                      <span className="text-sm text-gray-500">Uploading... {uploadProgress}%</span>
                    </div>
                  ) : (
                    <>
                      <Upload className="w-8 h-8 text-gray-400" />
                      <span className="mt-2 text-sm text-gray-500">
                        Click or drag and drop to upload cover image
                      </span>
                      <span className="mt-1 text-xs text-gray-400">
                        Recommended size: 1200×400 pixels (16:3 ratio)
                      </span>
                    </>
                  )}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Poster Image Upload */}
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Label htmlFor="posterImage" className="text-lg font-medium">
                  Poster Image
                </Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-4 w-4 text-gray-400" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">
                        This image will be displayed in the event gallery and listings.
                        Recommended size: 800×1200 pixels.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>

              {formData.posterImage && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleRemoveImage('poster')}
                  className="text-red-500"
                >
                  <X className="h-4 w-4 mr-1" />
                  Remove
                </Button>
              )}
            </div>

            {formData.posterImage ? (
              <div className="relative w-full max-w-[300px] h-[400px] rounded-md overflow-hidden border mx-auto">
                <Image
                  src={formData.posterImage.url}
                  alt="Poster Image"
                  fill
                  className="object-cover" />
              </div>
            ) : (
              <div
                className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${isDraggingPoster
                  ? 'border-primary bg-primary/5'
                  : 'border-gray-300 hover:border-primary/50'
                  }`}
                onDragEnter={(e) => handleDragEnter(e, 'poster')}
                onDragOver={handleDragOver}
                onDragLeave={(e) => handleDragLeave(e, 'poster')}
                onDrop={(e) => handleDrop(e, 'poster')}
              >
                <input
                  type="file"
                  id="posterImage"
                  accept="image/*"
                  onChange={(e) => handleFileUpload(e, 'poster')}
                  className="hidden"
                  disabled={!!isUploading}
                  ref={posterInputRef}
                />
                <div
                  onClick={() => triggerFileInput('poster')}
                  className="flex flex-col items-center justify-center cursor-pointer"
                >
                  {isUploading === 'poster' ? (
                    <div className="flex flex-col items-center space-y-2">
                      <Loader2 className="w-8 h-8 text-primary animate-spin" />
                      <span className="text-sm text-gray-500">Uploading... {uploadProgress}%</span>
                    </div>
                  ) : (
                    <>
                      <ImagePlus className="w-8 h-8 text-gray-400" />
                      <span className="mt-2 text-sm text-gray-500">
                        Click or drag and drop to upload poster image
                      </span>
                      <span className="mt-1 text-xs text-gray-400">
                        Recommended size: 800×1200 pixels (2:3 ratio)
                      </span>
                    </>
                  )}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Gallery Images */}
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Label htmlFor="galleryImages" className="text-lg font-medium">
                  Gallery Images
                </Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-4 w-4 text-gray-400" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">
                        Add up to {MAX_GALLERY_IMAGES} images to showcase in your event gallery.
                        Recommended size: 800×600 pixels.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>

              <input
                type="file"
                id="galleryImages"
                accept="image/*"
                multiple
                onChange={(e) => handleFileUpload(e, 'gallery')}
                className="hidden"
                disabled={isGalleryUploading}
                ref={galleryInputRef}
              />

              {error && (
                <div className="bg-red-50 border border-red-200 rounded-md p-4 mt-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <X className="h-5 w-5 text-red-400" aria-hidden="true" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-red-800">Upload Error</h3>
                      <div className="mt-2 text-sm text-red-700">
                        <p>{error}</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Gallery Preview */}
      {galleryImages.length > 0 && (
        <div className="space-y-4">
          <h4 className="text-sm font-medium">Gallery Preview</h4>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mt-4">
            {galleryImages.map((image, index) => (
              <div key={image.path} className="relative group">
                <div className="relative aspect-[4/3] rounded-md overflow-hidden border">
                  <Image
                    src={image.url}
                    alt={`Gallery Image ${index + 1}`}
                    fill
                    className="object-cover"
                  />
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleRemoveGalleryImage(index)}
                  className="absolute top-2 right-2 bg-white/90 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Image Focus Adjuster */}
      {formData.coverImage && isFocusAdjusterOpen && (
        <ImageFocusAdjuster
          imageUrl={formData.coverImage.url}
          isOpen={isFocusAdjusterOpen}
          onClose={() => setIsFocusAdjusterOpen(false)}
          onSave={handleSaveFocusPoint}
          initialFocusPoint={formData.coverImage.focusPoint || { y: 50 }}
        />
      )}
    </div>
  );
}
