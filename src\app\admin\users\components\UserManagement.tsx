'use client';

import { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { setUserRole } from '../../actions';
import { UserRole } from '../../../../types/roles';

// Define serializable user interface
interface SerializableUser {
  id: string;
  firstName: string | null;
  lastName: string | null;
  emailAddress: string | null;
  role: string;
  createdAt: number;
}

// Expected structure from /api/admin/users
interface ApiUser {
  id: string;
  firstName?: string | null;
  lastName?: string | null;
  emailAddresses?: Array<{ emailAddress: string | null }> | null;
  publicMetadata?: { role?: string | null } | null;
  createdAt: number;
}

interface UserManagementProps {
  initialUsers: SerializableUser[];
}

export function UserManagement({ initialUsers }: UserManagementProps) {
  const [users, setUsers] = useState<SerializableUser[]>(initialUsers);
  const [searchQuery, setSearchQuery] = useState('');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<SerializableUser | null>(null);
  const [selectedRole, setSelectedRole] = useState<UserRole>(UserRole.ADMIN);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      setUsers(initialUsers);
      return;
    }

    try {
      const res = await fetch(`/api/admin/users?search=${encodeURIComponent(searchQuery)}`);
      const data = await res.json();

      if (data.success && data.users) {
        // We need to serialize the returned users data in the same way
        const serializedUsers: SerializableUser[] = (data.users as ApiUser[]).map((user: ApiUser) => ({
          id: user.id,
          firstName: user.firstName || null,
          lastName: user.lastName || null,
          emailAddress: user.emailAddresses?.[0]?.emailAddress || null,
          role: (user.publicMetadata?.role as string) || UserRole.USER,
          createdAt: user.createdAt,
        }));
        setUsers(serializedUsers);
      }
    } catch (error) {
      console.error('Error searching users:', error);
    }
  };

  const handlePromoteUser = async () => {
    if (!selectedUser) return;

    setIsSubmitting(true);

    try {
      const result = await setUserRole({
        userId: selectedUser.id,
        role: selectedRole as UserRole
      });

      if (result.success) {
        alert(result.message);

        // Close the dialog
        setDialogOpen(false);

        // Update the user in the list with the new role
        setUsers(users.map(user =>
          user.id === selectedUser.id
            ? { ...user, role: selectedRole }
            : user
        ));
      } else {
        alert(result.message);
      }
    } catch (error) {
      console.error('Error promoting user:', error);
      alert("An unexpected error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  const openPromoteDialog = (user: SerializableUser) => {
    setSelectedUser(user);
    // Set initial role based on current user role
    setSelectedRole(user.role as UserRole || UserRole.ADMIN);
    setDialogOpen(true);
  };

  return (
    <div className="space-y-6">
      <div className="flex space-x-4">
        <div className="flex-1">
          <Input
            placeholder="Search users by name or email..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
          />
        </div>
        <Button onClick={handleSearch}>Search</Button>
      </div>

      <div className="rounded-md border overflow-x-auto">
        <table className="w-full min-w-full table-auto">
          <thead>
            <tr className="border-b bg-muted/50">
              <th className="px-4 py-3 text-left font-medium">Name</th>
              <th className="px-4 py-3 text-left font-medium">Email</th>
              <th className="px-4 py-3 text-left font-medium">Role</th>
              <th className="px-4 py-3 text-left font-medium">Actions</th>
            </tr>
          </thead>
          <tbody>
            {users.length === 0 ? (
              <tr>
                <td colSpan={4} className="text-center py-4">
                  No users found
                </td>
              </tr>
            ) : (
              users.map((user) => (
                <tr key={user.id} className="border-b">
                  <td className="px-4 py-3">
                    {user.firstName} {user.lastName}
                  </td>
                  <td className="px-4 py-3">{user.emailAddress}</td>
                  <td className="px-4 py-3">
                    <span className="capitalize">{user.role}</span>
                  </td>
                  <td className="px-4 py-3">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openPromoteDialog(user)}
                    >
                      Manage Role
                    </Button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {dialogOpen && selectedUser && (
        <div className="fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-card text-card-foreground rounded-lg p-6 w-full max-w-md border border-border shadow-lg">
            <h3 className="text-lg font-medium mb-2">Change User Role</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Update role for {selectedUser.firstName} {selectedUser.lastName} ({selectedUser.emailAddress})
            </p>

            <div className="py-4">
              <label className="block text-sm font-medium mb-2">
                Select Role
              </label>
              <select
                value={selectedRole}
                onChange={(e) => setSelectedRole(e.target.value as UserRole)}
                className="w-full bg-background border border-input rounded-md p-2"
              >
                <option value={UserRole.USER}>User</option>
                <option value={UserRole.EVENT_ORGANIZER}>Event Organizer</option>
                <option value={UserRole.ADMIN}>Admin</option>
              </select>
            </div>

            <div className="flex justify-end space-x-3 mt-4">
              <Button
                variant="outline"
                onClick={() => setDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handlePromoteUser}
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Updating...' : 'Update Role'}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}