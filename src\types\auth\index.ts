/**
 * Authentication and authorization type definitions
 * @module types/auth
 */

export * from './roles';

export interface User {
    id: string
    email: string
    first_name?: string
    last_name?: string | null
    avatar?: string | null
    created_at?: string
    updated_at?: string
}

export interface AuthState {
    user: User | null
    loading: boolean
    error: string | null
}

export interface SignInCredentials {
    email: string
    password: string
}

export interface SignUpCredentials extends SignInCredentials {
    first_name?: string
    last_name?: string
}

export interface AuthContextType {
    user: User | null
    signIn: (credentials: SignInCredentials) => Promise<void>
    signUp: (credentials: SignUpCredentials) => Promise<void>
    signOut: () => Promise<void>
} 