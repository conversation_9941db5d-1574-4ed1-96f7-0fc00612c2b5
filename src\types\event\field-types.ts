/**
 * Field type definitions
 * @module types/event/field-types
 */

import { z } from 'zod';

/**
 * Enum for field types
 */
export enum FieldType {
    // Text input types
    TEXT = 'text',
    TEXTAREA = 'textarea',
    EMAIL = 'email',
    PHONE = 'phone',

    // Number input
    NUMBER = 'number',

    // Selection types
    SELECT = 'select',
    MULTISELECT = 'multiselect',
    RADIO = 'radio',
    CHECKBOX = 'checkbox',

    // Date/Time types
    DATE = 'date',
    TIME = 'time',
    DATETIME = 'datetime',

    // File types
    FILE = 'file',
    IMAGE = 'image',

    // Display type
    DISPLAY = 'display'
}

/**
 * Base field interface
 * Contains common properties for all field types
 */
export interface BaseField {
    id: string;
    type: FieldType;
    label: string;
    description: string | null;
    placeholder: string | null;
    required: boolean;
    order: number;
}

/**
 * Field option type for select/multiselect fields
 */
export type FieldOption = {
    label: string;
    value: string;
};

/**
 * Validation rule types
 */
export interface TextValidationRules {
    type: 'text';
    minLength: number | null;
    maxLength: number | null;
    pattern: string | null;
    customMessage: string | null;
}

export interface NumberValidationRules {
    type: 'number';
    min: number | null;
    max: number | null;
    step: number | null;
    customMessage: string | null;
}

export interface DateValidationRules {
    type: 'date';
    min: string | null;
    max: string | null;
    customMessage: string | null;
}

export interface FileValidationRules {
    type: 'file';
    maxSize: number | null;
    allowedTypes: string[] | null;
    customMessage: string | null;
}

/**
 * Field type interfaces
 */
export interface TextField extends BaseField {
    type: FieldType.TEXT | FieldType.TEXTAREA | FieldType.EMAIL | FieldType.PHONE;
    defaultValue?: string;
    validationRules: TextValidationRules | null;
}

export interface NumberField extends BaseField {
    type: FieldType.NUMBER;
    defaultValue?: number;
    validationRules: NumberValidationRules | null;
}

export interface SelectField extends BaseField {
    type: FieldType.SELECT | FieldType.RADIO;
    options: FieldOption[];
    defaultValue?: string;
    validationRules: null;
}

export interface MultiSelectField extends BaseField {
    type: FieldType.MULTISELECT;
    options: FieldOption[];
    defaultValue?: string[];
    validationRules: null;
}

export interface CheckboxField extends BaseField {
    type: FieldType.CHECKBOX;
    defaultValue?: boolean;
    validationRules: null;
}

export interface DateField extends BaseField {
    type: FieldType.DATE | FieldType.TIME | FieldType.DATETIME;
    defaultValue?: string;
    validationRules: DateValidationRules | null;
}

export interface FileField extends BaseField {
    type: FieldType.FILE | FieldType.IMAGE;
    defaultValue?: string;
    validationRules: FileValidationRules | null;
}

export interface DisplayField extends BaseField {
    type: FieldType.DISPLAY;
    content: string;
    validationRules: null;
}

/**
 * Union type for all field types
 */
export type EventField =
    | TextField
    | NumberField
    | SelectField
    | MultiSelectField
    | CheckboxField
    | DateField
    | FileField
    | DisplayField;

/**
 * Zod schemas for validation
 */
const baseFieldSchema = z.object({
    id: z.string(),
    type: z.nativeEnum(FieldType),
    label: z.string(),
    description: z.string().nullable(),
    placeholder: z.string().nullable(),
    required: z.boolean(),
    order: z.number()
});

const fieldOptionSchema = z.object({
    label: z.string(),
    value: z.string()
});

const textValidationSchema = z.object({
    type: z.literal('text'),
    minLength: z.number().nullable(),
    maxLength: z.number().nullable(),
    pattern: z.string().nullable(),
    customMessage: z.string().nullable()
});

const numberValidationSchema = z.object({
    type: z.literal('number'),
    min: z.number().nullable(),
    max: z.number().nullable(),
    step: z.number().nullable(),
    customMessage: z.string().nullable()
});

const dateValidationSchema = z.object({
    type: z.literal('date'),
    min: z.string().nullable(),
    max: z.string().nullable(),
    customMessage: z.string().nullable()
});

const fileValidationSchema = z.object({
    type: z.literal('file'),
    maxSize: z.number().nullable(),
    allowedTypes: z.array(z.string()).nullable(),
    customMessage: z.string().nullable()
});

export const textFieldSchema = baseFieldSchema.extend({
    type: z.enum([FieldType.TEXT, FieldType.TEXTAREA, FieldType.EMAIL, FieldType.PHONE]),
    defaultValue: z.string().optional(),
    validationRules: textValidationSchema.nullable()
});

export const numberFieldSchema = baseFieldSchema.extend({
    type: z.literal(FieldType.NUMBER),
    defaultValue: z.number().optional(),
    validationRules: numberValidationSchema.nullable()
});

export const selectFieldSchema = baseFieldSchema.extend({
    type: z.enum([FieldType.SELECT, FieldType.RADIO]),
    options: z.array(fieldOptionSchema),
    defaultValue: z.string().optional(),
    validationRules: z.null()
});

export const multiSelectFieldSchema = baseFieldSchema.extend({
    type: z.literal(FieldType.MULTISELECT),
    options: z.array(fieldOptionSchema),
    defaultValue: z.array(z.string()).optional(),
    validationRules: z.null()
});

export const checkboxFieldSchema = baseFieldSchema.extend({
    type: z.literal(FieldType.CHECKBOX),
    defaultValue: z.boolean().optional(),
    validationRules: z.null()
});

export const dateFieldSchema = baseFieldSchema.extend({
    type: z.enum([FieldType.DATE, FieldType.TIME, FieldType.DATETIME]),
    defaultValue: z.string().optional(),
    validationRules: dateValidationSchema.nullable()
});

export const fileFieldSchema = baseFieldSchema.extend({
    type: z.enum([FieldType.FILE, FieldType.IMAGE]),
    defaultValue: z.string().optional(),
    validationRules: fileValidationSchema.nullable()
});

export const displayFieldSchema = baseFieldSchema.extend({
    type: z.literal(FieldType.DISPLAY),
    content: z.string(),
    validationRules: z.null()
});

/**
 * Combined schema for all field types
 */
export const eventFieldSchema = z.discriminatedUnion('type', [
    textFieldSchema,
    numberFieldSchema,
    selectFieldSchema,
    multiSelectFieldSchema,
    checkboxFieldSchema,
    dateFieldSchema,
    fileFieldSchema,
    displayFieldSchema
]); 