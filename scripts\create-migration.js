const fs = require('fs');
const path = require('path');

// Get the migration name from command line arguments
const migrationName = process.argv[2];
if (!migrationName) {
    console.error('Please provide a migration name');
    process.exit(1);
}

// Create migrations directory if it doesn't exist
const migrationsDir = path.join(__dirname, '../src/migrations');
if (!fs.existsSync(migrationsDir)) {
    fs.mkdirSync(migrationsDir, { recursive: true });
}

// Create migration file
const timestamp = new Date().toISOString().replace(/[^0-9]/g, '').slice(0, 14);
const fileName = `${timestamp}_${migrationName}.sql`;
const filePath = path.join(migrationsDir, fileName);

// Create empty migration file
fs.writeFileSync(filePath, `-- Migration: ${migrationName}\n\n-- Up\n\n-- Down\n`);

console.log(`Created migration file: ${fileName}`); 