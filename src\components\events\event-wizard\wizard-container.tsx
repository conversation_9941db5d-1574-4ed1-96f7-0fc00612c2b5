/* eslint-disable @typescript-eslint/no-unused-vars */
'use client';

import { useState, createContext, useContext, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { EventTypeStep } from '@/components/events/event-wizard/steps/event-type-step';
import { BasicDetailsStep } from '@/components/events/event-wizard/steps/basic-details-step';
import { CategoriesStep } from '@/components/events/event-wizard/steps/categories-step';
import { FieldsStep } from '@/components/events/event-wizard/steps/fields-step';
import { TshirtOptionsStep } from '@/components/events/event-wizard/steps/tshirt-options-step';
import { ImageUploadStep } from '@/components/events/event-wizard/steps/image-upload-step';
import { PreviewStep } from '@/components/events/event-wizard/steps/preview-step';
import { EventType, EventField, FieldType, <PERSON>Field } from '../../../types/event-types';
import { createEvent, saveEventDraft } from '@/app/actions/events';
import { toast } from '@/components/ui/use-toast';
import { z } from 'zod';
import { checkAuthentication } from '@/app/dashboard/events/debug';
import { Event } from '@/repositories/event-repository';
import { toISOString, toInputDateTime } from '@/lib/utils/date-utils';
import { createEventSchema, type FormData } from '@/lib/validations/event-schema';
import { CompletionRateIndicator } from './completion-rate';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { supabase } from '@/lib/supabase/pages-client';
import { logger } from '@/lib/logger';
import { useSupabase } from '@/components/providers/supabase-provider';
import { InputFieldBase } from '@/types/event/field-types';
import { isSelectField, isInputField } from '@/types/event/type-guards';

// Define wizard steps
export enum WizardStep {
  EVENT_TYPE = 0,
  BASIC_DETAILS = 1,
  CATEGORIES = 2,
  FIELDS = 3,
  TSHIRT_OPTIONS = 4,
  IMAGES = 5,
  PREVIEW = 6,
}

interface WizardContextType {
  formData: FormData;
  updateFormData: (data: Partial<FormData>) => void;
  currentStep: number;
  setCurrentStep: (step: number) => void;
  isValid: boolean;
  setIsValid: (valid: boolean) => void;
}

const WizardContext = createContext<WizardContextType>({
  formData: {} as FormData,
  updateFormData: () => { },
  currentStep: 0,
  setCurrentStep: () => { },
  isValid: false,
  setIsValid: () => { }
});

export function useWizard() {
  const context = useContext(WizardContext);
  if (!context) {
    throw new Error('useWizard must be used within a WizardProvider');
  }
  return context;
}

interface EventWizardProps {
  eventTypes: EventType[];
  existingEvent?: Event | null;
  pageTitle: string;
}

export function EventWizard({ eventTypes, existingEvent, pageTitle }: EventWizardProps) {
  const router = useRouter();
  const { supabase } = useSupabase();
  const [currentStep, setCurrentStep] = useState<WizardStep>(WizardStep.EVENT_TYPE);
  const [isValid, setIsValid] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Log existingEvent prop for debugging
  if (process.env.NODE_ENV === 'development') {
    logger.debug('[DEBUG] EventWizard received existingEvent:', existingEvent ? {
      id: existingEvent.id,
      title: existingEvent.title,
      hasCategories: Array.isArray(existingEvent.categories) && existingEvent.categories.length > 0,
      categoriesCount: Array.isArray(existingEvent.categories) ? existingEvent.categories.length : 0
    } : 'null');
  }

  const [formData, setFormData] = useState<FormData>(() => {
    if (existingEvent) {
      if (process.env.NODE_ENV === 'development') {
        logger.debug('[DEBUG] Initializing formData with existingEvent data');
      }

      const mappedFormData: FormData = {
        id: existingEvent.id,
        title: existingEvent.title,
        description: existingEvent.description || '',
        eventTypeId: existingEvent.eventTypeId,
        eventType: existingEvent.eventTypeId || '', // For compatibility with the schema
        location: existingEvent.location || '',
        country: existingEvent.country || '',
        state: existingEvent.state || '',
        startDate: existingEvent.startDate ? toInputDateTime(existingEvent.startDate) : '',
        endDate: existingEvent.endDate ? toInputDateTime(existingEvent.endDate) : '',
        timezone: existingEvent.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone,
        categories: existingEvent.categories || [],
        customFields: (existingEvent.customFields || []).map((field: EventField) => {
          const baseField: BaseField = {
            id: field.id,
            type: field.type,
            label: field.label,
            description: field.description || null,
            order: field.order || 0
          };

          if (isInputField(field)) {
            const inputField: InputFieldBase = {
              ...baseField,
              required: field.required || false,
              placeholder: field.placeholder || null,
              defaultValue: field.defaultValue || null,
              validationRules: field.validationRules || null
            };

            if (isSelectField(field)) {
              return {
                ...inputField,
                options: field.options || []
              } as EventField;
            }

            return inputField as EventField;
          }

          return baseField as EventField;
        }),
        emergencyContactSettings: existingEvent.emergencyContactSettings || {
          required: false,
          fields: ['name', 'phone', 'relationship'],
          allowSameForMultipleRegistrations: true,
        },
        city: existingEvent.city || '',
        status: existingEvent.status || 'draft',
        totalCapacity: existingEvent.totalCapacity || 0,
        registrationCloseDate: existingEvent.registrationCloseDate ? toInputDateTime(existingEvent.registrationCloseDate) : '',
        allowCategorySpecificClosingDates: existingEvent.allowCategorySpecificClosingDates || false,
        tshirtOptions: existingEvent.tshirtOptions || {
          enabled: false,
          sizes: [],
          description: null,
          sizeChartImage: null
        },
        posterImage: existingEvent.posterImage || null,
        coverImage: existingEvent.coverImage || null,
        galleryImages: existingEvent.galleryImages || null,
        registrationSettings: existingEvent.registrationSettings || {
          allowMultiple: false,
          maxRegistrationsPerUser: 1,
          requireApproval: false,
          allowWaitlist: false,
          waitlistLimit: 0,
          allowCancellation: false,
          cancellationDeadline: null,
          allowTransfer: false,
          transferDeadline: null,
          allowRefund: false,
          refundDeadline: null,
          refundPercentage: 0,
        },
      };
      return mappedFormData;
    }
    return {
      title: '',
      description: '',
      eventTypeId: '',
      eventType: '',
      location: '',
      country: '',
      state: '',
      city: '',
      startDate: '',
      endDate: '',
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      status: 'draft',
      categories: [],
      customFields: [],
      totalCapacity: 0,
      registrationCloseDate: '',
      allowCategorySpecificClosingDates: false,
      tshirtOptions: {
        enabled: false,
        sizes: [],
        description: null,
        sizeChartImage: null
      },
      emergencyContactSettings: {
        required: false,
        fields: ['name', 'phone', 'relationship'],
        allowSameForMultipleRegistrations: true,
      },
      registrationSettings: {
        allowMultiple: false,
        maxRegistrationsPerUser: 1,
        requireApproval: false,
        allowWaitlist: false,
        waitlistLimit: 0,
        allowCancellation: false,
        cancellationDeadline: null,
        allowTransfer: false,
        transferDeadline: null,
        allowRefund: false,
        refundDeadline: null,
        refundPercentage: 0,
      }
    };
  });

  // Function to determine the first incomplete step
  const determineFirstIncompleteStep = (eventData: FormData): WizardStep => {
    // If no event type is selected, start at the beginning
    if (!eventData.eventTypeId) {
      return WizardStep.EVENT_TYPE;
    }

    // Check basic details
    const hasBasicDetails = eventData.title &&
      eventData.description &&
      eventData.location &&
      eventData.country &&
      eventData.state &&
      eventData.city;
    if (!hasBasicDetails) {
      return WizardStep.BASIC_DETAILS;
    }

    // Check categories
    const hasCategories = Array.isArray(eventData.categories) && eventData.categories.length > 0;
    if (!hasCategories) {
      return WizardStep.CATEGORIES;
    }

    // Return the first step by default
    return WizardStep.EVENT_TYPE;
  };

  // Log formData changes
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      logger.debug('[DEBUG] formData state updated:', {
        id: formData.id,
        title: formData.title,
        hasEventType: !!formData.eventTypeId,
        hasCategories: Array.isArray(formData.categories) && formData.categories.length > 0,
        categoriesCount: Array.isArray(formData.categories) ? formData.categories.length : 0
      });
    }
  }, [formData]);

  // If we have an existing event, navigate to the first incomplete step
  useEffect(() => {
    if (existingEvent && currentStep === WizardStep.EVENT_TYPE) {
      const firstIncompleteStep = determineFirstIncompleteStep(formData);
      if (process.env.NODE_ENV === 'development') {
        logger.debug('[DEBUG] First incomplete step:', firstIncompleteStep);
      }
      setCurrentStep(firstIncompleteStep);
    }
  }, [existingEvent, currentStep, formData]);

  // Helper function to check if a step is accessible
  const isStepAccessible = (step: WizardStep): boolean => {
    // Event type step is always accessible
    if (step === WizardStep.EVENT_TYPE) return true;

    // Basic details step is accessible if event type is selected
    if (step === WizardStep.BASIC_DETAILS) {
      return !!formData.eventTypeId;
    }

    // Categories step is accessible if basic details are filled
    if (step === WizardStep.CATEGORIES) {
      return !!formData.title && !!formData.description && !!formData.location;
    }

    // Fields step is accessible if categories are selected
    if (step === WizardStep.FIELDS) {
      return Array.isArray(formData.categories) && formData.categories.length > 0;
    }

    // T-shirt options step is accessible if fields are set up
    if (step === WizardStep.TSHIRT_OPTIONS) {
      return true; // Always accessible after fields
    }

    // Images step is accessible if T-shirt options are set up
    if (step === WizardStep.IMAGES) {
      return true; // Always accessible after T-shirt options
    }

    // Preview step is accessible if images are uploaded
    if (step === WizardStep.PREVIEW) {
      return true; // Always accessible after images
    }

    return false;
  };

  // Update form data with type checking
  const updateFormData = useCallback((newData: Partial<FormData>) => {
    setFormData((prevData) => {
      const updatedData = {
        ...prevData,
        ...newData,
      };

      // Handle custom fields
      if (newData.customFields) {
        updatedData.customFields = newData.customFields.map((field) => {
          const baseField: BaseField = {
            id: field.id,
            type: field.type,
            label: field.label,
            description: field.description || null,
            order: field.order || 0
          };

          if (isInputField(field)) {
            const inputField: InputFieldBase = {
              ...baseField,
              required: field.required || false,
              placeholder: field.placeholder || null,
              defaultValue: field.defaultValue || null,
              validationRules: field.validationRules || null
            };

            if (isSelectField(field)) {
              return {
                ...inputField,
                options: field.options || []
              } as EventField;
            }

            return inputField as EventField;
          }

          return baseField as EventField;
        });
      }

      return updatedData;
    });
  }, []);

  // Save draft
  const saveDraft = async () => {
    try {
      if (!formData.eventTypeId) {
        return;
      }

      const result = await saveEventDraft({
        title: formData.title,
        description: formData.description || '',
        eventType: formData.eventType || '',
        location: formData.location || '',
        country: formData.country || '',
        state: formData.state || '',
        city: formData.city || '',
        startDate: formData.startDate,
        endDate: formData.endDate,
        timezone: formData.timezone,
        categories: formData.categories,
        customFields: formData.customFields,
        emergencyContactSettings: formData.emergencyContactSettings,
        status: formData.status,
        allowCategorySpecificClosingDates: formData.allowCategorySpecificClosingDates,
        tshirtOptions: formData.tshirtOptions,
        posterImage: formData.posterImage,
        coverImage: formData.coverImage,
      });

      if (result.success && result.data) {
        updateFormData({ id: result.data.id });
        if (process.env.NODE_ENV === 'development') {
          logger.debug('[DEBUG] Auto-save updated form data with new ID:', result.data.id);
        }
      }
    } catch (error) {
      console.error('Error saving draft:', error);
    }
  };

  // Submit form
  const submitForm = async () => {
    try {
      setIsSubmitting(true);
      setValidationErrors({});

      // Validate required fields
      const validationResult = await createEventSchema.safeParseAsync(formData);
      if (!validationResult.success) {
        const errors = validationResult.error.flatten().fieldErrors;
        setValidationErrors(
          Object.entries(errors).reduce((acc, [key, value]) => ({
            ...acc,
            [key]: value?.[0] || '',
          }), {})
        );
        return;
      }

      // Create event
      const eventData: FormData = {
        ...formData,
        customFields: formData.customFields?.map((field) => {
          const baseField: BaseField = {
            id: field.id,
            type: field.type,
            label: field.label,
            description: field.description || null,
            order: field.order || 0
          };

          if (isInputField(field)) {
            const inputField: InputFieldBase = {
              ...baseField,
              required: field.required || false,
              placeholder: field.placeholder || null,
              defaultValue: field.defaultValue || null,
              validationRules: field.validationRules || null
            };

            if (isSelectField(field)) {
              return {
                ...inputField,
                options: field.options || []
              } as EventField;
            }

            return inputField as EventField;
          }

          return baseField as EventField;
        }) || []
      };

      const result = await createEvent(eventData);
      if (result.error) {
        throw new Error(result.error);
      }

      toast({
        title: 'Success',
        description: 'Event created successfully',
      });

      // Redirect to event list
      router.push('/dashboard/events');
    } catch (error) {
      console.error('Error creating event:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An error occurred while creating the event',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Step components rendering
  const renderStep = () => {
    switch (currentStep) {
      case WizardStep.EVENT_TYPE:
        return <EventTypeStep eventTypes={eventTypes} />;
      case WizardStep.BASIC_DETAILS:
        return <BasicDetailsStep />;
      case WizardStep.CATEGORIES:
        return <CategoriesStep />;
      case WizardStep.FIELDS:
        return <FieldsStep />;
      case WizardStep.TSHIRT_OPTIONS:
        return <TshirtOptionsStep />;
      case WizardStep.IMAGES:
        return <ImageUploadStep />;
      case WizardStep.PREVIEW:
        return <PreviewStep />;
      default:
        return null;
    }
  };

  // Handle step click for direct navigation
  const handleStepClick = async (stepNum: number) => {
    // Don't do anything if clicking the current step
    if (stepNum === currentStep) {
      return;
    }

    // If trying to go forward, validate current step first
    if (stepNum > currentStep) {
      if (!isStepAccessible(stepNum as WizardStep)) {
        return;
      }
    }

    // Auto-save before changing steps
    try {
      if (formData.title || formData.eventTypeId) {
        if (process.env.NODE_ENV === 'development') {
          logger.debug('[DEBUG] Auto-saving before step navigation');
        }
        await autoSaveDraft();
      }
    } catch (error) {
      console.error('[DEBUG] Error auto-saving before step navigation:', error);
      // Continue even if auto-save fails
    }

    // Navigate to the selected step
    setCurrentStep(stepNum as WizardStep);

    // Scroll to the top of the page smoothly after step change
    setTimeout(() => {
      const pageHeading = document.querySelector('h1') as HTMLElement;
      if (pageHeading) {
        pageHeading.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 100);
  };

  // Auto-save draft
  const autoSaveDraft = async () => {
    try {
      if (!supabase) {
        console.error('[DEBUG] Supabase client is not available');
        return;
      }

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.error('[DEBUG] Authentication check failed during auto-save - No user found');
        return;
      }

      // Format data to match event schema with proper types
      const preparedData: FormData = {
        ...formData,
        eventType: formData.eventTypeId || '',
        description: formData.description || '',
        location: formData.location || '',
        country: formData.country || '',
        state: formData.state || '',
        city: formData.city || '',
        status: formData.status || 'draft',
        categories: formData.categories || [],
        customFields: formData.customFields?.map((field) => {
          const baseField: BaseField = {
            id: field.id,
            type: field.type,
            label: field.label,
            description: field.description || null,
            order: field.order || 0
          };

          if (isInputField(field)) {
            const inputField: InputFieldBase = {
              ...baseField,
              required: field.required || false,
              placeholder: field.placeholder || null,
              defaultValue: field.defaultValue || null,
              validationRules: field.validationRules || null
            };

            if (isSelectField(field)) {
              return {
                ...inputField,
                options: field.options || []
              } as EventField;
            }

            return inputField as EventField;
          }

          return baseField as EventField;
        }) || [],
        tshirtOptions: {
          enabled: formData.tshirtOptions?.enabled ?? false,
          sizes: formData.tshirtOptions?.sizes ?? [],
          description: formData.tshirtOptions?.description ?? null,
          sizeChartImage: formData.tshirtOptions?.sizeChartImage ?? null
        },
        allowCategorySpecificClosingDates: formData.allowCategorySpecificClosingDates ?? false,
      };

      const result = await saveEventDraft(preparedData);
      if (result.error) {
        console.error('[DEBUG] Error saving draft:', result.error);
      }
    } catch (error) {
      console.error('[DEBUG] Error in auto-save:', error);
    }
  };

  // Navigation functions
  const nextStep = useCallback(async () => {
    if (currentStep < WizardStep.PREVIEW) {
      // Validate current step before proceeding
      if (!isStepAccessible(currentStep as WizardStep)) {
        return;
      }

      // Auto-save the current state before moving to the next step
      try {
        // Only auto-save if we have at least a title or event type selected
        if (formData.title || formData.eventTypeId) {
          if (process.env.NODE_ENV === 'development') {
            logger.debug('[DEBUG] Auto-saving before moving to next step');
          }
          await autoSaveDraft();
        }
      } catch (error) {
        console.error('[DEBUG] Error auto-saving before step change:', error);
        // Continue even if auto-save fails
      }

      // If moving to the preview step, make sure we're not automatically submitting
      const nextStepValue = currentStep + 1;
      setCurrentStep(nextStepValue as WizardStep);

      // Scroll to the top of the page smoothly after step change
      setTimeout(() => {
        const pageHeading = document.querySelector('h1') as HTMLElement;
        if (pageHeading) {
          pageHeading.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }, 100);
    }
  }, [currentStep, formData, autoSaveDraft]);

  const prevStep = useCallback(async () => {
    if (currentStep > WizardStep.EVENT_TYPE) {
      // Auto-save the current state before moving to the previous step
      try {
        // Only auto-save if we have at least a title or event type selected
        if (formData.title || formData.eventTypeId) {
          if (process.env.NODE_ENV === 'development') {
            logger.debug('[DEBUG] Auto-saving before moving to previous step');
          }
          await autoSaveDraft();
        }
      } catch (error) {
        console.error('[DEBUG] Error auto-saving before step change:', error);
        // Continue even if auto-save fails
      }

      setCurrentStep(currentStep - 1 as WizardStep);
    }
  }, [currentStep, formData, autoSaveDraft]);

  const isLastStep = currentStep === WizardStep.PREVIEW;
  const isFirstStep = currentStep === WizardStep.EVENT_TYPE;

  return (
    <WizardContext.Provider
      value={{
        formData,
        updateFormData,
        nextStep,
        prevStep,
        currentStep,
        isLastStep,
        isFirstStep,
        isValid,
        setIsValid
      }}
    >
      <div className="container mx-auto py-8">
        <div className="max-w-4xl mx-auto">
          <div className="space-y-8">
            <div className="flex items-center justify-between">
              <h1 className="text-2xl font-bold">{pageTitle}</h1>
              <CompletionRateIndicator step={currentStep} />
            </div>

            <div className="flex justify-center">
              <div className="flex items-center space-x-4">
                {Object.values(WizardStep)
                  .filter((step): step is WizardStep => typeof step === 'number')
                  .map((stepNum) => (
                    <div key={stepNum} className="relative">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <button
                              type="button"
                              tabIndex={stepNum <= currentStep || isStepAccessible(stepNum) ? 0 : -1}
                              aria-label={`Go to ${stepNum === WizardStep.EVENT_TYPE ? 'Event Type' :
                                stepNum === WizardStep.BASIC_DETAILS ? 'Basic Details' :
                                  stepNum === WizardStep.CATEGORIES ? 'Categories' :
                                    stepNum === WizardStep.FIELDS ? 'Custom Fields' :
                                      stepNum === WizardStep.TSHIRT_OPTIONS ? 'T-Shirt Options' :
                                        stepNum === WizardStep.IMAGES ? 'Images' :
                                          'Preview'
                                } step`}
                              className={`relative flex items-center justify-center w-8 h-8 rounded-full border ${stepNum === currentStep
                                ? 'bg-primary text-primary-foreground'
                                : stepNum < currentStep
                                  ? 'bg-primary/20 text-primary'
                                  : 'bg-background'
                                } ${stepNum <= currentStep || isStepAccessible(stepNum)
                                  ? 'cursor-pointer hover:bg-primary hover:text-primary-foreground'
                                  : 'cursor-not-allowed opacity-50'
                                }`}
                              onClick={() => handleStepClick(stepNum)}
                              disabled={!(stepNum <= currentStep || isStepAccessible(stepNum))}
                            >
                              {stepNum + 1}
                            </button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>
                              {stepNum === WizardStep.EVENT_TYPE ? 'Event Type' :
                                stepNum === WizardStep.BASIC_DETAILS ? 'Basic Details' :
                                  stepNum === WizardStep.CATEGORIES ? 'Categories' :
                                    stepNum === WizardStep.FIELDS ? 'Custom Fields' :
                                      stepNum === WizardStep.TSHIRT_OPTIONS ? 'T-Shirt Options' :
                                        stepNum === WizardStep.IMAGES ? 'Images' :
                                          'Preview'}
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  ))}
              </div>
            </div>

            <Card>
              <CardContent className="pt-6">
                {renderStep()}
              </CardContent>
            </Card>

            <div className="flex justify-between mt-8">
              {currentStep > WizardStep.EVENT_TYPE && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={prevStep}
                  disabled={isSubmitting}
                >
                  Previous
                </Button>
              )}

              {currentStep < WizardStep.PREVIEW ? (
                <Button
                  type="button"
                  onClick={nextStep}
                  disabled={isSubmitting}
                >
                  Next
                </Button>
              ) : (
                <Button
                  type="button"
                  onClick={submitForm}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Publishing...' : 'Publish Event'}
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </WizardContext.Provider>
  );
}