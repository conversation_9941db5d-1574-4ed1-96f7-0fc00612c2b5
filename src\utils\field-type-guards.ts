import { FieldType, <PERSON><PERSON>ield, <PERSON><PERSON>ield, TextField, NumberField, <PERSON>splay<PERSON>ield } from '@/types/event-types';

/**
 * Type guard for select fields
 */
export function isSelectField(field: EventField): field is SelectField {
    return [FieldType.SELECT, FieldType.MULTISELECT, FieldType.RADIO].includes(field.type);
}

/**
 * Type guard for text fields
 */
export function isTextField(field: EventField): field is TextField {
    return [FieldType.TEXT, FieldType.EMAIL, FieldType.PHONE].includes(field.type);
}

/**
 * Type guard for number fields
 */
export function isNumberField(field: EventField): field is NumberField {
    return field.type === FieldType.NUMBER;
}

/**
 * Type guard for display fields
 */
export function isDisplayField(field: EventField): field is DisplayField {
    return [FieldType.HEADING, FieldType.PARAGRAPH, FieldType.DIVIDER].includes(field.type);
}

/**
 * Helper function to check if options are string array
 */
export function isStringOptions(options: SelectField['options']): options is string[] {
    return Array.isArray(options) && (options.length === 0 || typeof options[0] === 'string');
}

/**
 * Helper function to check if options are object array
 */
export function isObjectOptions(options: SelectField['options']): options is { label: string; value: string }[] {
    return Array.isArray(options) && (options.length === 0 || (typeof options[0] === 'object' && options[0] !== null));
}

/**
 * Helper function to get display value from option
 */
export function getOptionDisplayValue(option: string | { label: string; value: string }): string {
    return typeof option === 'string' ? option : option.label;
} 