/**
 * Details step component for event wizard
 * @module components/events/event-wizard/steps/details-step
 */

'use client';

import { useEffect } from 'react';
import { useWizard } from '../wizard-container';
import { FormData } from '@/lib/validations/event-schema';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { FormField } from '@/components/ui/form-field';
import { DatePicker } from '@/components/ui/date-picker';

export function DetailsStep() {
    const { formData, updateFormData, setIsValid } = useWizard();

    const handleInputChange = (field: keyof FormData, value: any) => {
        updateFormData({ [field]: value });
    };

    const handleDateChange = (field: keyof FormData) => (date: Date | undefined) => {
        updateFormData({ [field]: date?.toISOString() });
    };

    const parseDate = (dateString: string | undefined): Date | undefined => {
        if (!dateString) return undefined;
        const date = new Date(dateString);
        return isNaN(date.getTime()) ? undefined : date;
    };

    useEffect(() => {
        // Validate the form data
        const isValid = Boolean(
            formData.title &&
            formData.description &&
            formData.startDate &&
            formData.endDate &&
            formData.location &&
            formData.capacity &&
            formData.registrationDeadline
        );

        setIsValid(isValid);
    }, [formData, setIsValid]);

    return (
        <div className="space-y-4">
            <FormField>
                <Label>Title</Label>
                <Input
                    value={formData.title || ''}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="Enter event title"
                />
            </FormField>

            <FormField>
                <Label>Description</Label>
                <Textarea
                    value={formData.description || ''}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Enter event description"
                />
            </FormField>

            <div className="grid grid-cols-2 gap-4">
                <FormField>
                    <Label>Start Date</Label>
                    <DatePicker
                        selected={parseDate(formData.startDate)}
                        onSelect={handleDateChange('startDate')}
                    />
                </FormField>

                <FormField>
                    <Label>End Date</Label>
                    <DatePicker
                        selected={parseDate(formData.endDate)}
                        onSelect={handleDateChange('endDate')}
                        minDate={parseDate(formData.startDate)}
                    />
                </FormField>
            </div>

            <FormField>
                <Label>Location</Label>
                <Input
                    value={formData.location || ''}
                    onChange={(e) => handleInputChange('location', e.target.value)}
                    placeholder="Enter event location"
                />
            </FormField>

            <FormField>
                <Label>Capacity</Label>
                <Input
                    type="number"
                    value={formData.capacity || ''}
                    onChange={(e) => handleInputChange('capacity', parseInt(e.target.value, 10))}
                    placeholder="Enter event capacity"
                    min={1}
                />
            </FormField>

            <FormField>
                <Label>Registration Deadline</Label>
                <DatePicker
                    selected={parseDate(formData.registrationDeadline)}
                    onSelect={handleDateChange('registrationDeadline')}
                    maxDate={parseDate(formData.startDate)}
                />
            </FormField>
        </div>
    );
} 