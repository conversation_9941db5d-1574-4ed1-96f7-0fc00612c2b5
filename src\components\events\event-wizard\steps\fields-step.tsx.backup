'use client';

import { useState, useEffect } from 'react';
import { useWizard } from '@/components/events/event-wizard/wizard-container';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { X, Edit2, GripVertical, Plus, AlertCircle } from 'lucide-react';
import { z } from 'zod';
import { toast } from '@/components/ui/use-toast';
import { FieldType, EventField } from '@/types/event-types';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import {
  isSelectField,
  isTextField,
  isNumberField,
  isDisplayField,
  isStringOptions,
  isObjectOptions,
  getOptionDisplayValue
} from '@/utils/field-type-guards';

// Type for wizard context
interface WizardContextType {
  formData: {
    customFields?: EventField[];
  };
  updateFormData: (data: Partial<{ customFields?: EventField[] }>) => void;
  nextStep: () => void;
}

// Field validation schema
const fieldSchema = z.object({
  id: z.string().min(1, 'Field ID is required'),
  type: z.nativeEnum(FieldType),
  label: z.string().min(1, 'Label is required'),
  description: z.string().optional(),
  required: z.boolean().optional(),
  placeholder: z.string().optional(),
  options: z.array(
    z.union([
      z.string(),
      z.object({ label: z.string(), value: z.string() })
    ])
  ).optional(),
  minLength: z.number().optional(),
  maxLength: z.number().optional(),
  min: z.number().optional(),
  max: z.number().optional(),
  step: z.number().optional(),
  content: z.string().optional(),
  defaultValue: z.any().optional(),
  order: z.number().optional(),
})
  .superRefine((data, ctx) => {
    if ([FieldType.SELECT, FieldType.MULTISELECT, FieldType.RADIO].includes(data.type)) {
      if (!data.options || data.options.length === 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "At least one option is required for this field type",
          path: ["options"]
        });
      }
    }
  });

// Field type options for dropdown
const fieldTypeOptions = [
  { value: FieldType.TEXT, label: 'Text' },
  { value: FieldType.EMAIL, label: 'Email' },
  { value: FieldType.PHONE, label: 'Phone' },
  { value: FieldType.NUMBER, label: 'Number' },
  { value: FieldType.SELECT, label: 'Select' },
  { value: FieldType.MULTISELECT, label: 'Multi-select' },
  { value: FieldType.CHECKBOX, label: 'Checkbox' },
  { value: FieldType.RADIO, label: 'Radio' },
  { value: FieldType.DATE, label: 'Date' },
  { value: FieldType.TIME, label: 'Time' },
  { value: FieldType.DATETIME, label: 'Date & Time' },
  { value: FieldType.HEADING, label: 'Heading' },
  { value: FieldType.PARAGRAPH, label: 'Paragraph' },
  { value: FieldType.DIVIDER, label: 'Divider' },
];

// Default new field
const defaultField: TextField = {
  id: `field-${Date.now()}`,
  type: FieldType.TEXT,
  label: '',
  description: '',
  required: false,
  order: fields.length,
  placeholder: ''
};

// Start editing a new field
const addNewField = () => {
  setEditingField({ ...defaultField, id: `field-${Date.now()}` });
  setEditingIndex(null);
  setValidationErrors({});
};

// Edit an existing field
const editField = (index: number) => {
  if (index < 0 || index >= fields.length) {
    console.error('Invalid field index:', index);
    return;
  }

  const field = fields[index];
  if (!field) {
    console.error('Field not found at index:', index);
    return;
  }

  setEditingField({ ...field });
  setEditingIndex(index);
  setValidationErrors({});
};

// Delete a field
const deleteField = (index: number) => {
  const updatedFields = [...fields];
  updatedFields.splice(index, 1);

  // Update order numbers
  const reorderedFields = updatedFields.map((field, idx) => ({
    ...field,
    order: idx,
  }));

  setFields(reorderedFields);
  updateFormData({ customFields: reorderedFields });
};

// Handle drag and drop reordering
const onDragEnd = (result: DropResult) => {
  if (!result.destination) return;

  const sourceIndex = result.source.index;
  const destinationIndex = result.destination.index;

  // Reorder fields
  const reorderedFields = [...fields];
  const [removed] = reorderedFields.splice(sourceIndex, 1);
  reorderedFields.splice(destinationIndex, 0, removed);

  // Update order numbers
  const updatedFields = reorderedFields.map((field, index) => ({
    ...field,
    order: index,
  }));

  setFields(updatedFields);
  updateFormData({ customFields: updatedFields });
};

// Handle field type change
const handleFieldTypeChange = (type: FieldType) => {
  if (!editingField) return;

  const newField: EventField = {
    ...editingField,
    type,
    // Reset field-specific properties when type changes
    options: isSelectField({ ...editingField, type }) ? [] : undefined,
    min: isNumberField({ ...editingField, type }) ? undefined : undefined,
    max: isNumberField({ ...editingField, type }) ? undefined : undefined,
    step: isNumberField({ ...editingField, type }) ? undefined : undefined,
    content: isDisplayField({ ...editingField, type }) ? '' : undefined,
  };

  setEditingField(newField);
};

// Handle input change for field form
const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
  const { name, value } = e.target;

  setEditingField(prev => {
    if (!prev) return prev;
    return { ...prev, [name]: value };
  });

  // Clear validation error when field is edited
  if (validationErrors[name]) {
    setValidationErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[name];
      return newErrors;
    });
  }
};

// Handle number input change
const handleNumberInputChange = (name: string) => (value: string) => {
  const numberValue = value === '' ? undefined : Number(value);

  setEditingField(prev => {
    if (!prev) return prev;
    return { ...prev, [name]: numberValue };
  });

  // Clear validation error when field is edited
  if (validationErrors[name]) {
    setValidationErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[name];
      return newErrors;
    });
  }
};

// Handle switch toggle
const handleSwitchChange = (name: string) => (checked: boolean) => {
  setEditingField(prev => {
    if (!prev) return prev;
    return { ...prev, [name]: checked };
  });
};

// Handle option change for select/radio fields
const handleOptionChange = (index: number, value: string) => {
  if (!editingField || !isSelectField(editingField)) return;

  if (isStringOptions(editingField.options)) {
    // Handle string options
    const newOptions = [...editingField.options];
    newOptions[index] = value;

    setEditingField({
      ...editingField,
      options: newOptions
    });
  } else if (isObjectOptions(editingField.options)) {
    // Handle object options
    const newOptions = [...editingField.options] as { label: string; value: string }[];
    newOptions[index] = {
      ...newOptions[index],
      label: value,
      value: value.toLowerCase().replace(/\s+/g, '-')
    };

    setEditingField({
      ...editingField,
      options: newOptions
    });
  }
};

// Add a new option for select/radio fields
const addOption = () => {
  if (!editingField) return;

  if (isSelectField(editingField)) {
    // Handle existing SelectField
    if (isStringOptions(editingField.options)) {
      // For string options array
      const stringOptions = [...editingField.options];
      stringOptions.push(`Option ${stringOptions.length + 1}`);

      setEditingField({
        ...editingField,
        options: stringOptions
      });
    } else {
      // For object options array (isObjectOptions must be true here)
      // Create a copy that TypeScript can understand as the correct type
      const objectOptions = [...editingField.options].map(
        opt => typeof opt === 'object' ? opt : { label: opt, value: opt }
      ) as { label: string; value: string }[];

      objectOptions.push({
        label: `Option ${objectOptions.length + 1}`,
        value: `option-${objectOptions.length + 1}`
      });

      setEditingField({
        ...editingField,
        options: objectOptions
      });
    }
  } else if ([FieldType.SELECT, FieldType.MULTISELECT, FieldType.RADIO].includes(editingField.type)) {
    // Create a new SelectField with string array options
    const newField: SelectField = {
      ...editingField,
      type: editingField.type as FieldType.SELECT | FieldType.MULTISELECT | FieldType.RADIO,
      options: ['Option 1']
    };

    setEditingField(newField);
  }
};

// Remove an option for select/radio fields
const removeOption = (index: number) => {
  if (!editingField || !isSelectField(editingField)) return;

  const newOptions = [...editingField.options];
  newOptions.splice(index, 1);

  setEditingField({
    ...editingField,
    options: newOptions
  });
};

// Validate and save field
const saveField = () => {
  if (!editingField) return;

  try {
    // Validate field data
    fieldSchema.parse(editingField);

    // Update fields array
    const updatedFields = [...fields];
    if (editingIndex !== null) {
      updatedFields[editingIndex] = editingField;
    } else {
      updatedFields.push(editingField);
    }

    // Update order numbers
    const reorderedFields = updatedFields.map((field, index) => ({
      ...field,
      order: index,
    }));

    setFields(reorderedFields);
    updateFormData({ customFields: reorderedFields });
    setEditingField(null);
    setEditingIndex(null);
    setValidationErrors({});

    toast({
      title: editingIndex !== null ? 'Field updated' : 'Field added',
      description: `Successfully ${editingIndex !== null ? 'updated' : 'added'} the field.`,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors: Record<string, string> = {};
      error.errors.forEach((err) => {
        if (err.path) {
          errors[err.path[0]] = err.message;
        }
      });
      setValidationErrors(errors);
    } else {
      console.error('Validation error:', error);
      toast({
        title: 'Error',
        description: 'Failed to save field. Please check your input.',
        variant: 'destructive',
      });
    }
  }
};

// Cancel editing
const cancelEditing = () => {
  setEditingField(null);
  setEditingIndex(null);
  setValidationErrors({});
};

// Generate the field preview
const getFieldPreview = (field: EventField | null) => {
  if (!field) return null;

  switch (field.type) {
    case FieldType.TEXT:
    case FieldType.EMAIL:
    case FieldType.PHONE:
      if (isTextField(field)) {
        return (
          <Input
            type={field.type.toLowerCase()}
            placeholder={field.placeholder}
            disabled
          />
        );
      }
      break;

    case FieldType.NUMBER:
      if (isNumberField(field)) {
        return (
          <Input
            type="number"
            placeholder={field.placeholder}
            min={field.min}
            max={field.max}
            step={field.step}
            disabled
          />
        );
      }
      break;

    case FieldType.SELECT:
    case FieldType.MULTISELECT:
      if (isSelectField(field)) {
        return (
          <Select disabled>
            <SelectTrigger>
              <SelectValue placeholder={field.placeholder || 'Select...'} />
            </SelectTrigger>
          </Select>
        );
      }
      break;

    case FieldType.CHECKBOX:
      if (field.type === FieldType.CHECKBOX) {
        return <Checkbox disabled />;
      }
      break;

    case FieldType.RADIO:
      if (isSelectField(field)) {
        return (
          <div className="flex gap-2">
            {field.options?.map((option, index) => (
              <div key={index} className="flex items-center space-x-2">
                <input type="radio" disabled />
                <span>{getOptionDisplayValue(option)}</span>
              </div>
            ))}
          </div>
        );
      }
      break;

    case FieldType.DATE:
    case FieldType.TIME:
    case FieldType.DATETIME:
      return <Input type={field.type.toLowerCase()} disabled />;

    case FieldType.HEADING:
    case FieldType.PARAGRAPH:
      if (isDisplayField(field)) {
        return field.type === FieldType.HEADING ? (
          <h3 className="text-lg font-semibold">{field.content}</h3>
        ) : (
          <p className="text-gray-600">{field.content}</p>
        );
      }
      break;

    case FieldType.DIVIDER:
      return <hr className="my-2" />;
  }

  return null;
};

// Helper function to get options safely
const getFieldOptions = (field: EventField) => {
  if (isSelectField(field)) {
    return field.options;
  }
  return [];
};

export function FieldsStep() {
  const { formData, updateFormData, nextStep } = useWizard() as WizardContextType;
  const [fields, setFields] = useState<EventField[]>(formData.customFields || []);
  const [editingField, setEditingField] = useState<EventField | null>(null);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Add autofocus to appropriate field when editing
  useEffect(() => {
    if (editingField) {
      // Focus on field type select when adding/editing a field
      setTimeout(() => {
        const fieldTypeSelect = document.querySelector('[id*="fieldType"]') as HTMLElement;
        if (fieldTypeSelect) {
          fieldTypeSelect.focus();
        }
      }, 100);
    } else {
      // Focus on add new field button if no field is being edited
      const addButton = document.querySelector('button:has(svg[data-icon="plus"])') as HTMLElement;
      if (addButton) {
        addButton.focus();
      }
    }
  }, [editingField]);

  // Save fields to form data when they change
  useEffect(() => {
    updateFormData({ customFields: fields });
  }, [fields, updateFormData]);

  // Handle field type change
  const handleFieldTypeChange = (type: FieldType) => {
    if (!editingField) return;

    const newField: EventField = {
      ...editingField,
      type,
      // Reset field-specific properties when type changes
      options: isSelectField({ ...editingField, type }) ? [] : undefined,
      min: isNumberField({ ...editingField, type }) ? undefined : undefined,
      max: isNumberField({ ...editingField, type }) ? undefined : undefined,
      step: isNumberField({ ...editingField, type }) ? undefined : undefined,
      content: isDisplayField({ ...editingField, type }) ? '' : undefined,
    };

    setEditingField(newField);
  };

  // Handle field save
  const handleSave = () => {
    if (!editingField) return;

    try {
      // Validate field
      fieldSchema.parse(editingField);

      // Update fields array
      const newFields = [...fields];
      if (editingIndex !== null) {
        newFields[editingIndex] = editingField;
      } else {
        newFields.push(editingField);
      }

      setFields(newFields);
      setEditingField(null);
      setEditingIndex(null);
      setValidationErrors({});
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors: Record<string, string> = {};
        error.errors.forEach((err) => {
          const path = err.path.join('.');
          errors[path] = err.message;
        });
        setValidationErrors(errors);
      }
    }
  };

  // Handle field deletion
  const handleDelete = (index: number) => {
    const newFields = [...fields];
    newFields.splice(index, 1);
    setFields(newFields);
  };

  // Handle field edit
  const handleEdit = (field: EventField, index: number) => {
    setEditingField(field);
    setEditingIndex(index);
  };

  // Handle drag and drop reordering
  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    const newFields = [...fields];
    const [removed] = newFields.splice(result.source.index, 1);
    newFields.splice(result.destination.index, 0, removed);
    setFields(newFields);
  };

  // Handle adding a new field
  const handleAddField = () => {
    const newField: EventField = {
      id: crypto.randomUUID(),
      type: FieldType.TEXT,
      label: '',
      required: false,
    };
    setEditingField(newField);
    setEditingIndex(null);
  };

  // Render field preview
  const renderFieldPreview = (field: EventField) => {
    if (isTextField(field)) {
      return (
        <div className="flex flex-col gap-2">
          <Label>{field.label}</Label>
          <Input type={field.type} placeholder={field.placeholder} disabled />
        </div>
      );
    }

    if (isNumberField(field)) {
      return (
        <div className="flex flex-col gap-2">
          <Label>{field.label}</Label>
          <Input
            type="number"
            placeholder={field.placeholder}
            min={field.min}
            max={field.max}
            step={field.step}
            disabled
          />
        </div>
      );
    }

    if (isSelectField(field)) {
      return (
        <div className="flex flex-col gap-2">
          <Label>{field.label}</Label>
          <Select disabled>
            <SelectTrigger>
              <SelectValue placeholder={field.placeholder} />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map((option, index) => (
                <SelectItem
                  key={index}
                  value={typeof option === 'string' ? option : option.value}
                >
                  {getOptionDisplayValue(option)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      );
    }

    if (field.type === FieldType.CHECKBOX) {
      return (
        <div className="flex items-center gap-2">
          <Checkbox id={field.id} disabled />
          <Label htmlFor={field.id}>{field.label}</Label>
        </div>
      );
    }

    if (isDisplayField(field)) {
      if (field.type === FieldType.HEADING) {
        return <h3 className="text-lg font-semibold">{field.content || field.label}</h3>;
      }
      if (field.type === FieldType.PARAGRAPH) {
        return <p className="text-sm text-muted-foreground">{field.content || field.label}</p>;
      }
      return <hr className="my-4" />;
    }

    return null;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold">Custom Fields</h2>
        <Button
          variant="outline"
          size="sm"
          onClick={handleAddField}
          disabled={!!editingField}
        >
          <Plus className="mr-2 h-4 w-4" />
          Add Field
        </Button>
      </div>

      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="fields">
          {(provided) => (
            <div
              {...provided.droppableProps}
              ref={provided.innerRef}
              className="space-y-4"
            >
              {fields.map((field, index) => (
                <Draggable
                  key={field.id}
                  draggableId={field.id}
                  index={index}
                >
                  {(provided) => (
                    <Card
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      className="relative"
                    >
                      <CardHeader className="pb-4">
                        <div className="flex items-center justify-between">
                          <div
                            {...provided.dragHandleProps}
                            className="cursor-grab hover:text-primary"
                          >
                            <GripVertical className="h-5 w-5" />
                          </div>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleEdit(field, index)}
                              className="h-8 w-8"
                            >
                              <Edit2 className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleDelete(index)}
                              className="h-8 w-8"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        {renderFieldPreview(field)}
                        {field.description && (
                          <p className="mt-2 text-sm text-muted-foreground">
                            {field.description}
                          </p>
                        )}
                      </CardContent>
                    </Card>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>

      {editingField && (
        <Card>
          <CardHeader>
            <CardTitle>
              {editingIndex !== null ? 'Edit Field' : 'Add Field'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <Label htmlFor="fieldType">Field Type</Label>
                <Select
                  value={editingField.type}
                  onValueChange={(value) => handleFieldTypeChange(value as FieldType)}
                >
                  <SelectTrigger id="fieldType">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {fieldTypeOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {validationErrors.type && (
                  <p className="mt-1 text-sm text-destructive">
                    {validationErrors.type}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="label">Label</Label>
                <Input
                  id="label"
                  value={editingField.label}
                  onChange={(e) =>
                    setEditingField({ ...editingField, label: e.target.value })
                  }
                />
                {validationErrors.label && (
                  <p className="mt-1 text-sm text-destructive">
                    {validationErrors.label}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="description">Description (Optional)</Label>
                <Textarea
                  id="description"
                  value={editingField.description || ''}
                  onChange={(e) =>
                    setEditingField({
                      ...editingField,
                      description: e.target.value,
                    })
                  }
                />
              </div>

              {(isTextField(editingField) ||
                isNumberField(editingField) ||
                isSelectField(editingField)) && (
                  <div>
                    <Label htmlFor="placeholder">Placeholder (Optional)</Label>
                    <Input
                      id="placeholder"
                      value={editingField.placeholder || ''}
                      onChange={(e) =>
                        setEditingField({
                          ...editingField,
                          placeholder: e.target.value,
                        })
                      }
                    />
                  </div>
                )}

              {isSelectField(editingField) && (
                <div>
                  <Label>Options</Label>
                  <div className="space-y-2">
                    {editingField.options?.map((option, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <Input
                          value={getOptionDisplayValue(option)}
                          onChange={(e) => {
                            const newOptions = [...editingField.options];
                            newOptions[index] = e.target.value;
                            setEditingField({
                              ...editingField,
                              options: newOptions,
                            });
                          }}
                        />
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            const newOptions = [...editingField.options];
                            newOptions.splice(index, 1);
                            setEditingField({
                              ...editingField,
                              options: newOptions,
                            });
                          }}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                    <Button
                      variant="outline"
                      onClick={() => {
                        setEditingField({
                          ...editingField,
                          options: [...(editingField.options || []), ''],
                        });
                      }}
                    >
                      Add Option
                    </Button>
                  </div>
                  {validationErrors.options && (
                    <p className="mt-1 text-sm text-destructive">
                      {validationErrors.options}
                    </p>
                  )}
                </div>
              )}

              {isNumberField(editingField) && (
                <>
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="min">Min</Label>
                      <Input
                        id="min"
                        type="number"
                        value={editingField.min || ''}
                        onChange={(e) =>
                          setEditingField({
                            ...editingField,
                            min: e.target.value ? Number(e.target.value) : undefined,
                          })
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor="max">Max</Label>
                      <Input
                        id="max"
                        type="number"
                        value={editingField.max || ''}
                        onChange={(e) =>
                          setEditingField({
                            ...editingField,
                            max: e.target.value ? Number(e.target.value) : undefined,
                          })
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor="step">Step</Label>
                      <Input
                        id="step"
                        type="number"
                        value={editingField.step || ''}
                        onChange={(e) =>
                          setEditingField({
                            ...editingField,
                            step: e.target.value ? Number(e.target.value) : undefined,
                          })
                        }
                      />
                    </div>
                  </div>
                </>
              )}

              {isDisplayField(editingField) && editingField.type !== FieldType.DIVIDER && (
                <div>
                  <Label htmlFor="content">Content</Label>
                  <Textarea
                    id="content"
                    value={editingField.content || ''}
                    onChange={(e) =>
                      setEditingField({
                        ...editingField,
                        content: e.target.value,
                      })
                    }
                  />
                </div>
              )}

              <div className="flex items-center gap-2">
                <Switch
                  id="required"
                  checked={editingField.required || false}
                  onCheckedChange={(checked) =>
                    setEditingField({ ...editingField, required: checked })
                  }
                />
                <Label htmlFor="required">Required</Label>
              </div>

              <div className="flex justify-end gap-4">
                <Button
                  variant="outline"
                  onClick={() => {
                    setEditingField(null);
                    setEditingIndex(null);
                    setValidationErrors({});
                  }}
                >
                  Cancel
                </Button>
                <Button onClick={handleSave}>Save</Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="flex justify-end">
        <Button onClick={nextStep} disabled={fields.length === 0}>
          Next
        </Button>
      </div>
    </div>
  );
}