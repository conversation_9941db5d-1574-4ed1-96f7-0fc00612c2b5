import { NextResponse } from "next/server";
import { createAdminClient } from '@/lib/supabase/pages-client';
import { asUnknown, type ExtendedSupabaseClient } from '@/lib/supabase/extended-types';

/**
 * This API route provides instructions for fixing the schema cache issue
 * in Supabase where the emergency contact fields are not recognized.
 * 
 * The issue occurs because the schema cache in Supabase/PostgREST is out of sync
 * with the actual database schema. This requires a schema cache refresh.
 */
export async function GET() {
  const instructions = {
    title: "Schema Cache Fix Instructions",
    description: "Follow these steps to fix the schema cache issue:",
    steps: [
      {
        step: 1,
        title: "Access Supabase SQL Editor",
        description: "Log in to your Supabase dashboard and open the SQL Editor"
      },
      {
        step: 2,
        title: "Run the migration script",
        description: "Execute the following SQL to refresh column types and force schema cache update:",
        sql: `
-- Fix emergency contact fields in saved_contacts table
ALTER TABLE saved_contacts ALTER COLUMN emergency_contact_name TYPE TEXT;
ALTER TABLE saved_contacts ALTER COLUMN emergency_contact_no TYPE TEXT;
ALTER TABLE saved_contacts ALTER COLUMN emergency_contact_relationship TYPE TEXT;

-- Update basic columns as well to ensure full refresh
ALTER TABLE saved_contacts ALTER COLUMN first_name TYPE TEXT;
ALTER TABLE saved_contacts ALTER COLUMN last_name TYPE TEXT;

-- Force schema cache reload (may require admin privileges)
SELECT pg_notify('pgrst', 'reload schema');`
      },
      {
        step: 3,
        title: "Restart PostgREST if needed",
        description: "If the schema cache still isn't refreshed, you may need to restart the PostgREST service from your Supabase dashboard or contact Supabase support."
      },
      {
        step: 4,
        title: "Test the fix",
        description: "Test if the schema cache is updated by running a simple query:",
        sql: "SELECT emergency_contact_name FROM saved_contacts LIMIT 1;"
      },
      {
        step: 5,
        title: "Re-enable emergency contact fields in code",
        description: "Once the schema cache is fixed, uncomment the emergency contact fields in the REST API call in src/app/actions/contacts.ts"
      }
    ],
    additionalNotes: "The root cause of this issue is that the Supabase schema cache gets out of sync with actual database schema. Altering column types forces a refresh of the schema metadata in the cache."
  };

  return NextResponse.json(instructions);
}

export async function POST(request: Request) {
  try {
    // Get request body to check for secret key
    let secretKey = '';
    try {
      const body = await request.json();
      secretKey = body?.secretKey || '';
    } catch (e) {
      // Ignore JSON parsing errors
    }

    // Check if secret key matches - only allow admin operations with correct key
    const validSecret = process.env.ADMIN_SECRET_KEY;
    if (!validSecret || secretKey !== validSecret) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const supabase = await createAdminClient();
    const supabaseAny = asUnknown(supabase) as ExtendedSupabaseClient;

    // Step 1: Alter column types to force schema refresh
    const queries = [
      'ALTER TABLE saved_contacts ALTER COLUMN first_name TYPE TEXT;',
      'ALTER TABLE saved_contacts ALTER COLUMN last_name TYPE TEXT;',
      'ALTER TABLE saved_contacts ALTER COLUMN relationship TYPE TEXT;',
      'ALTER TABLE saved_contacts ALTER COLUMN emergency_contact_name TYPE TEXT;',
      'ALTER TABLE saved_contacts ALTER COLUMN emergency_contact_no TYPE TEXT;',
      'ALTER TABLE saved_contacts ALTER COLUMN emergency_contact_relationship TYPE TEXT;',
    ];

    const results = [];

    for (const query of queries) {
      const { data, error } = await supabaseAny.rpc('public.pg_execute', { query });
      results.push({ query, success: !error, errorMessage: error?.message });

      if (error) {
        console.error(`Error executing query: ${query}`, error);
      }
    }

    // Step 2: Force schema cache reload
    const { data: notifyData, error: notifyError } = await supabaseAny.rpc('pg_notify', {
      channel: 'pgrst',
      payload: 'reload schema'
    });

    // Step 3: Test the schema
    const { data: testData, error: testError } = await supabaseAny
      .from('saved_contacts')
      .select('first_name')
      .limit(1);

    return NextResponse.json({
      success: !testError,
      queries: results,
      schemaReload: { success: !notifyError, error: notifyError?.message },
      test: { success: !testError, error: testError?.message },
      message: testError
        ? 'Schema fix may not be complete. You might need to restart the PostgREST service.'
        : 'Schema cache successfully refreshed!'
    });
  } catch (error) {
    console.error('Error fixing schema cache:', error);
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
} 