#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Fix Import Statements
 * Fixes import statements that were incorrectly modified by previous automation
 */

// Files that have broken import statements based on lint output
const brokenFiles = [
    'src/app/(legal)/cookies/page.tsx',
    'src/app/(legal)/privacy-policy/page.tsx',
    'src/app/(legal)/terms/page.tsx',
    'src/app/about/page.tsx',
    'src/app/admin/migrations/registration-fields/page.tsx',
    'src/app/api/user/profile/route.ts',
    'src/app/events/registration-success/page.tsx',
    'src/components/dashboard/header.tsx',
    'src/components/events/dashboard/event-actions.tsx',
    'src/components/profile/privacy-settings.tsx'
];

/**
 * Fix import and require statements
 */
function fixImportStatements(content) {
    const fixes = [];
    let result = content;

    // Fix import statements with HTML entities
    const importPatterns = [
        {
            pattern: /import\s+([^;]+)\s+from\s+&apos;([^&]+)&apos;/g,
            replacement: "import $1 from '$2'",
            description: 'Fixed import statement with &apos; entities'
        },
        {
            pattern: /import\s+([^;]+)\s+from\s+&quot;([^&]+)&quot;/g,
            replacement: 'import $1 from "$2"',
            description: 'Fixed import statement with &quot; entities'
        },
        {
            pattern: /from\s+&apos;([^&]+)&apos;/g,
            replacement: "from '$1'",
            description: 'Fixed from clause with &apos; entities'
        },
        {
            pattern: /from\s+&quot;([^&]+)&quot;/g,
            replacement: 'from "$1"',
            description: 'Fixed from clause with &quot; entities'
        }
    ];

    importPatterns.forEach(pattern => {
        const beforeCount = (result.match(pattern.pattern) || []).length;
        if (beforeCount > 0) {
            result = result.replace(pattern.pattern, pattern.replacement);
            fixes.push(`${pattern.description} (${beforeCount} instances)`);
        }
    });

    return { content: result, fixes };
}

/**
 * Process a single file
 */
function processFile(filePath) {
    try {
        if (!fs.existsSync(filePath)) {
            console.log(`⚠️  File not found: ${filePath}`);
            return false;
        }

        const content = fs.readFileSync(filePath, 'utf8');
        const { content: result, fixes } = fixImportStatements(content);

        if (result !== content) {
            fs.writeFileSync(filePath, result, 'utf8');
            console.log(`✅ Fixed: ${path.relative(process.cwd(), filePath)}`);
            fixes.forEach(fix => console.log(`   - ${fix}`));
            return true;
        } else {
            console.log(`⚪ No changes needed: ${path.relative(process.cwd(), filePath)}`);
            return false;
        }
    } catch (error) {
        console.error(`❌ Error processing ${filePath}:`, error.message);
        return false;
    }
}

/**
 * Main execution
 */
function main() {
    console.log('🔧 Fixing broken import statements...\n');

    let processedCount = 0;
    let modifiedCount = 0;

    brokenFiles.forEach(relativePath => {
        const fullPath = path.join(process.cwd(), relativePath);
        processedCount++;

        if (processFile(fullPath)) {
            modifiedCount++;
        }
    });

    console.log('\n📊 Import Statement Fix Summary:');
    console.log(`   Files processed: ${processedCount}`);
    console.log(`   Files fixed: ${modifiedCount}`);
    console.log(`   Files unchanged: ${processedCount - modifiedCount}`);

    if (modifiedCount > 0) {
        console.log('\n🎉 Import statements fixed! This should resolve many parsing errors.');
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = { fixImportStatements, processFile }; 