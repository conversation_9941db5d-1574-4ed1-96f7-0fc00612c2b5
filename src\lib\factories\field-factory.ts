/**
 * Field factory for creating form fields
 * @module lib/factories/field-factory
 */

import { v4 as uuidv4 } from 'uuid';
import { FieldType } from '@/types/event/field-base';
import type {
    EventField,
    TextField,
    NumberField,
    SelectField,
    DateField,
    FileField,
    DisplayField,
    CustomField,
    FieldOption
} from '@/types/event/field-types';

/**
 * Base options for creating a field
 */
interface BaseFieldOptions {
    id?: string;
    label: string;
    description?: string | null;
    order?: number;
}

/**
 * Options for creating an input field
 */
interface InputFieldOptions extends BaseFieldOptions {
    required?: boolean;
    placeholder?: string | null;
    defaultValue?: unknown | null;
    validationRules?: Record<string, unknown> | null;
}

/**
 * Factory class for creating form fields
 */
export class FieldFactory {
    /**
     * Creates a text field
     */
    static createTextField(
        type: typeof FieldType.TEXT | typeof FieldType.TEXTAREA | typeof FieldType.EMAIL | typeof FieldType.PHONE,
        options: InputFieldOptions & {
            minLength?: number | null;
            maxLength?: number | null;
        }
    ): TextField {
        return {
            id: options.id || uuidv4(),
            type,
            label: options.label,
            description: options.description ?? null,
            order: options.order ?? 0,
            required: options.required ?? false,
            placeholder: options.placeholder ?? null,
            defaultValue: options.defaultValue ?? null,
            validationRules: options.validationRules ?? null,
            minLength: options.minLength ?? null,
            maxLength: options.maxLength ?? null,
        };
    }

    /**
     * Creates a number field
     */
    static createNumberField(options: InputFieldOptions & {
        min?: number | null;
        max?: number | null;
        step?: number | null;
    }): NumberField {
        return {
            id: options.id || uuidv4(),
            type: FieldType.NUMBER,
            label: options.label,
            description: options.description ?? null,
            order: options.order ?? 0,
            required: options.required ?? false,
            placeholder: options.placeholder ?? null,
            defaultValue: options.defaultValue ?? null,
            validationRules: options.validationRules ?? null,
            min: options.min ?? null,
            max: options.max ?? null,
            step: options.step ?? null,
        };
    }

    /**
     * Creates a select field
     */
    static createSelectField(
        type: typeof FieldType.SELECT | typeof FieldType.MULTISELECT | typeof FieldType.CHECKBOX | typeof FieldType.RADIO,
        options: InputFieldOptions & {
            options: FieldOption[];
        }
    ): SelectField {
        return {
            id: options.id || uuidv4(),
            type,
            label: options.label,
            description: options.description ?? null,
            order: options.order ?? 0,
            required: options.required ?? false,
            placeholder: options.placeholder ?? null,
            defaultValue: options.defaultValue ?? null,
            validationRules: options.validationRules ?? null,
            options: options.options,
        };
    }

    /**
     * Creates a date field
     */
    static createDateField(
        type: typeof FieldType.DATE | typeof FieldType.TIME | typeof FieldType.DATETIME,
        options: InputFieldOptions & {
            min?: string | null;
            max?: string | null;
        }
    ): DateField {
        return {
            id: options.id || uuidv4(),
            type,
            label: options.label,
            description: options.description ?? null,
            order: options.order ?? 0,
            required: options.required ?? false,
            placeholder: options.placeholder ?? null,
            defaultValue: options.defaultValue ?? null,
            validationRules: options.validationRules ?? null,
            min: options.min ?? null,
            max: options.max ?? null,
        };
    }

    /**
     * Creates a file field
     */
    static createFileField(
        type: typeof FieldType.FILE | typeof FieldType.IMAGE,
        options: InputFieldOptions & {
            accept?: string | null;
            maxSize?: number | null;
        }
    ): FileField {
        return {
            id: options.id || uuidv4(),
            type,
            label: options.label,
            description: options.description ?? null,
            order: options.order ?? 0,
            required: options.required ?? false,
            placeholder: options.placeholder ?? null,
            defaultValue: options.defaultValue ?? null,
            validationRules: options.validationRules ?? null,
            accept: options.accept ?? null,
            maxSize: options.maxSize ?? null,
        };
    }

    /**
     * Creates a display field
     */
    static createDisplayField(
        type: typeof FieldType.HEADING | typeof FieldType.PARAGRAPH | typeof FieldType.DIVIDER,
        options: BaseFieldOptions & {
            content?: string | null;
        }
    ): DisplayField {
        return {
            id: options.id || uuidv4(),
            type,
            label: options.label,
            description: options.description ?? null,
            order: options.order ?? 0,
            content: options.content ?? null,
        };
    }

    /**
     * Creates a custom field
     */
    static createCustomField(options: InputFieldOptions & {
        config: Record<string, unknown>;
    }): CustomField {
        return {
            id: options.id || uuidv4(),
            type: FieldType.CUSTOM,
            label: options.label,
            description: options.description ?? null,
            order: options.order ?? 0,
            required: options.required ?? false,
            placeholder: options.placeholder ?? null,
            defaultValue: options.defaultValue ?? null,
            validationRules: options.validationRules ?? null,
            config: options.config,
        };
    }
} 