@echo off
echo Fixing HTML entities in TypeScript and React files...

powershell -Command "Get-ChildItem -Recurse -Path src -Include *.tsx,*.ts,*.jsx,*.js | ForEach-Object { $content = Get-Content $_.FullName -Raw; $original = $content; $content = $content -replace [char]38 + 'quot;', [char]34; $content = $content -replace [char]38 + 'apos;', [char]39; $content = $content -replace [char]38 + 'gt;', '>'; $content = $content -replace [char]38 + 'lt;', '<'; $content = $content -replace [char]38 + 'amp;', [char]38; if ($content -ne $original) { Set-Content -Path $_.FullName -Value $content -NoNewline; Write-Host \"Fixed: $($_.FullName)\" } }"

echo.
echo HTML entity fix completed!
echo Running type-check to verify...
pnpm type-check 