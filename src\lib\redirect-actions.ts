'use server';

import { redirect } from 'next/navigation';
import { getBaseUrl } from '@/utils/url-utilities';

/**
 * Interface for redirect options
 */
interface RedirectOptions {
  /** Whether to add no_redirect=true to prevent redirect loops */
  noRedirect?: boolean;

  /** Whether to add a timestamp to prevent caching issues */
  addTimestamp?: boolean;

  /** Additional query parameters to add to the URL */
  queryParams?: Record<string, string>;

  /** Whether to use an absolute URL */
  absolute?: boolean;
}

/**
 * Default redirect options
 */
const defaultRedirectOptions = {
  noRedirect: false,
  addTimestamp: true,
  queryParams: {},
  absolute: false,
};

/**
 * Safely redirect to a URL with standardized parameters
 * @param url The URL to redirect to
 * @param options Redirect options
 */
export async function safeRedirect(url: string, options: RedirectOptions = {}) {
  // Merge options with defaults
  const opts = { ...defaultRedirectOptions, ...options };

  // Create a URL object
  const urlObj = new URL(
    url.startsWith('http')
      ? url
      : `${opts.absolute ? getBaseUrl() : ''}${url.startsWith('/') ? url : `/${url}`}`
  );

  // Add no_redirect=true to prevent redirect loops
  if (opts.noRedirect) {
    urlObj.searchParams.set('no_redirect', 'true');
  }

  // Add a timestamp to prevent caching issues
  if (opts.addTimestamp) {
    urlObj.searchParams.set('_ts', Date.now().toString());
  }

  // Add additional query parameters
  if (opts.queryParams) {
    Object.entries(opts.queryParams).forEach(([key, value]) => {
      if (typeof value === 'string') {
        urlObj.searchParams.set(key, value);
      }
    });
  }

  // Redirect to the URL
  redirect(urlObj.toString());
}

/**
 * Redirect to the sign-in page with the current URL as the redirect target
 * @param redirectUrl The URL to redirect to after sign-in
 * @param options Additional redirect options
 */
export async function redirectToSignIn(redirectUrl?: string, options: RedirectOptions = {}) {
  const signInUrl = new URL('/sign-in', getBaseUrl());

  // Add the redirect URL if provided
  if (redirectUrl) {
    signInUrl.searchParams.set('redirect_url', redirectUrl);
  }

  // Add additional query parameters
  if (options.queryParams) {
    Object.entries(options.queryParams).forEach(([key, value]) => {
      signInUrl.searchParams.set(key, value);
    });
  }

  // Add a timestamp to prevent caching issues
  if (options.addTimestamp !== false) {
    signInUrl.searchParams.set('_ts', Date.now().toString());
  }

  // Redirect to the sign-in page
  redirect(signInUrl.toString());
}

/**
 * Redirect to the dashboard with standardized parameters
 * @param options Redirect options
 */
export async function redirectToDashboard(options: RedirectOptions = {}) {
  await safeRedirect('/dashboard', {
    noRedirect: true,
    ...options
  });
}
