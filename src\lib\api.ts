import { createClient } from '@/lib/supabase/client';

export interface UpdateProfileResponse {
    success: boolean;
    error?: string;
}

export interface FormData {
    first_name: string;
    last_name?: string;
    username?: string;
    gender?: string;
    bio?: string;
    avatar?: string;
    isPublic?: number;
    eventCategories?: string[];
    nationality?: string;
    ic?: string;
    passport?: string;
    dateOfBirth?: string;
    contactNo?: string;
    address?: string;
    apartment?: string;
    city?: string;
    postcode?: string;
    country?: string;
    state?: string;
    emergencyContactName?: string;
    emergencyContactNo?: string;
    emergencyContactRelationship?: string;
    tshirtSize?: string;
}

export async function updateProfile(data: FormData): Promise<UpdateProfileResponse> {
    try {
        const supabase = createClient();

        const { error } = await supabase
            .from('users')
            .update({
                first_name: data.first_name,
                last_name: data.last_name,
                username: data.username,
                gender: data.gender,
                bio: data.bio,
                avatar: data.avatar,
                isPublic: data.isPublic,
                eventCategories: data.eventCategories ? JSON.stringify(data.eventCategories) : null,
                nationality: data.nationality,
                ic: data.ic,
                passport: data.passport,
                dateOfBirth: data.dateOfBirth,
                contactNo: data.contactNo,
                address: data.address,
                apartment: data.apartment,
                city: data.city,
                postcode: data.postcode,
                country: data.country,
                state: data.state,
                emergencyContactName: data.emergencyContactName,
                emergencyContactNo: data.emergencyContactNo,
                emergencyContactRelationship: data.emergencyContactRelationship,
                tshirt_size: data.tshirtSize,
                updated_at: new Date().toISOString()
            })
            .single();

        if (error) throw error;

        return { success: true };
    } catch (error) {
        console.error('Error updating profile:', error);
        return {
            success: false,
            error: error instanceof Error ? error.message : 'An unknown error occurred'
        };
    }
} 