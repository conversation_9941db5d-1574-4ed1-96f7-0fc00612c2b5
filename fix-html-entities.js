const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Function to fix HTML entities in a file
function fixHtmlEntities(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        let modified = false;

        // Replace HTML entities
        const originalContent = content;
        content = content.replace(/&quot;/g, '"');
        content = content.replace(/&apos;/g, "'");
        content = content.replace(/&gt;/g, '>');
        content = content.replace(/&lt;/g, '<');
        content = content.replace(/&amp;/g, '&');

        if (content !== originalContent) {
            fs.writeFileSync(filePath, content, 'utf8');
            modified = true;
        }

        return modified;
    } catch (error) {
        console.error(`Error processing ${filePath}:`, error.message);
        return false;
    }
}

// Find all TypeScript and React files
const patterns = [
    'src/**/*.ts',
    'src/**/*.tsx',
    'src/**/*.js',
    'src/**/*.jsx'
];

console.log('🔍 Finding files with HTML entities...');

let totalFiles = 0;
let modifiedFiles = 0;

patterns.forEach(pattern => {
    const files = glob.sync(pattern);

    files.forEach(file => {
        totalFiles++;
        const modified = fixHtmlEntities(file);
        if (modified) {
            modifiedFiles++;
            console.log(`✅ Fixed: ${file}`);
        }
    });
});

console.log(`\n📊 Summary:`);
console.log(`- Total files checked: ${totalFiles}`);
console.log(`- Files modified: ${modifiedFiles}`);
console.log(`- Files unchanged: ${totalFiles - modifiedFiles}`);

if (modifiedFiles > 0) {
    console.log('\n🎉 HTML entities have been fixed! Run type-check again to verify.');
} else {
    console.log('\n✨ No HTML entities found to fix.');
} 