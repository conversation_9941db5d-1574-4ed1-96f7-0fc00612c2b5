#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Fix Error Handling Pattern
 * Fixes the specific pattern causing parsing errors: { error instanceof Error ? error.message : "..." }
 */

// Files with the problematic pattern
const filesToFix = [
    'src/app/actions/activity.ts',
    'src/app/actions/contacts.ts',
    'src/app/actions/data-export.ts',
    'src/app/actions/privacy.ts',
    'src/app/actions/apply-migration.ts',
    'src/app/actions/event-actions.ts',
    'src/app/actions/events.ts'
];

/**
 * Fix the error handling pattern
 */
function fixErrorHandling(content) {
    // Pattern: { error instanceof Error ? error.message : "message" }
    // Fix: { error: error instanceof Error ? error.message : "message" }

    const pattern = /\{\s*error\s+instanceof\s+Error\s*\?\s*error\.message\s*:\s*([^}]+)\s*\}/g;

    const fixed = content.replace(pattern, (match, fallbackMessage) => {
        return `{ error: error instanceof Error ? error.message : ${fallbackMessage} }`;
    });

    return fixed;
}

/**
 * Process a single file
 */
function processFile(filePath) {
    try {
        if (!fs.existsSync(filePath)) {
            console.log(`⚠️  File not found: ${filePath}`);
            return false;
        }

        const content = fs.readFileSync(filePath, 'utf8');
        const fixedContent = fixErrorHandling(content);

        if (content !== fixedContent) {
            fs.writeFileSync(filePath, fixedContent, 'utf8');
            console.log(`✅ Fixed error handling in: ${filePath}`);
            return true;
        } else {
            console.log(`⚪ No changes needed: ${filePath}`);
            return false;
        }
    } catch (error) {
        console.error(`❌ Error processing ${filePath}:`, error.message);
        return false;
    }
}

/**
 * Main execution
 */
function main() {
    console.log('🔧 Fixing error handling patterns...\n');

    let fixedCount = 0;
    let totalCount = 0;

    filesToFix.forEach(relativePath => {
        const fullPath = path.join(process.cwd(), relativePath);
        totalCount++;

        if (processFile(fullPath)) {
            fixedCount++;
        }
    });

    console.log('\n📊 Error Handling Fix Summary:');
    console.log(`   Files processed: ${totalCount}`);
    console.log(`   Files fixed: ${fixedCount}`);
    console.log(`   Files unchanged: ${totalCount - fixedCount}`);

    if (fixedCount > 0) {
        console.log('\n🎉 Error handling patterns fixed! This should resolve parsing errors.');
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = { fixErrorHandling, processFile }; 