import { createClient } from '@/lib/supabase/pages-client'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

export const dynamic = 'force-dynamic'

/**
 * Debug endpoint to check the current session state
 * This is useful for troubleshooting authentication issues
 */
export async function GET(_request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = await createClient()

    // Get the current session
    const { data: { session }, error } = await supabase.auth.getSession()

    // Get the current user
    const authUser = session?.user

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // Get user data from the database if authenticated
    let userData = null
    if (authUser) {
      const { data: user, error: userError } = await supabase
        .from('users')
        .select('id, role, display_name, email')
        .eq('auth_user_id', authUser.id)
        .single()

      if (!userError && user) {
        userData = user
      }
    }

    // Return session info (or null if not authenticated)
    return NextResponse.json({
      authenticated: !!session,
      sessionDetails: session ? {
        user: {
          id: session.user.id,
          email: session.user.email,
        },
        expires_at: session.expires_at,
      } : null,
      userData,
      cookies: {
        names: Array.from(cookieStore.getAll()).map(c => c.name)
      },
      now: new Date().toISOString(),
    })
  } catch (err) {
    console.error('Error in debug-session route:', err)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}