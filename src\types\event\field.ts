/**
 * Event field type definitions
 * @module types/event/field
 */

/**
 * Available field types for event registration forms
 */
export enum FieldType {
    TEXT = 'text',
    EMAIL = 'email',
    NUMBER = 'number',
    PHONE = 'phone',
    SELECT = 'select',
    MULTISELECT = 'multiselect',
    CHECKBOX = 'checkbox',
    RADIO = 'radio',
    DATE = 'date',
    TIME = 'time',
    DATETIME = 'datetime',
    FILE = 'file',
    ADDRESS = 'address',
    HEADING = 'heading',
    PARAGRAPH = 'paragraph',
    DIVIDER = 'divider',
}

/**
 * Base field definition interface
 * Contains common properties for all field types
 */
export interface FieldDefinition {
    /** Unique identifier for the field */
    id: string;
    /** Type of the field */
    type: FieldType;
    /** Display label for the field */
    label: string;
    /** Optional description/help text */
    description?: string;
    /** Whether the field is required */
    required?: boolean;
    /** Display order of the field */
    order?: number;
}

/**
 * Text field definition
 * Used for text, email, and phone input fields
 */
export interface TextField extends FieldDefinition {
    type: FieldType.TEXT | FieldType.EMAIL | FieldType.PHONE;
    /** Placeholder text */
    placeholder?: string;
    /** Minimum length constraint */
    minLength?: number;
    /** Maximum length constraint */
    maxLength?: number;
    /** Validation pattern */
    pattern?: string;
}

/**
 * Number field definition
 */
export interface NumberField extends FieldDefinition {
    type: FieldType.NUMBER;
    /** Placeholder text */
    placeholder?: string;
    /** Minimum value constraint */
    min?: number;
    /** Maximum value constraint */
    max?: number;
    /** Step value for number input */
    step?: number;
}

/**
 * Option type for select fields
 * Can be either a string or an object with label and value
 */
export type FieldOption = string | { label: string; value: string };

/**
 * Select field definition
 * Used for select, multi-select, and radio inputs
 */
export interface SelectField extends FieldDefinition {
    type: FieldType.SELECT | FieldType.MULTISELECT | FieldType.RADIO;
    /** Available options */
    options: FieldOption[];
    /** Placeholder text */
    placeholder?: string;
}

/**
 * Checkbox field definition
 */
export interface CheckboxField extends FieldDefinition {
    type: FieldType.CHECKBOX;
    /** Default checked state */
    defaultValue?: boolean;
}

/**
 * Date/time field definition
 */
export interface DateTimeField extends FieldDefinition {
    type: FieldType.DATE | FieldType.TIME | FieldType.DATETIME;
    /** Minimum date/time constraint */
    min?: string;
    /** Maximum date/time constraint */
    max?: string;
}

/**
 * Display field definition
 * Used for headings, paragraphs, and dividers
 */
export interface DisplayField extends FieldDefinition {
    type: FieldType.HEADING | FieldType.PARAGRAPH | FieldType.DIVIDER;
    /** Content text */
    content?: string;
}

/**
 * Union type for all field definitions
 */
export type EventField =
    | TextField
    | NumberField
    | SelectField
    | CheckboxField
    | DateTimeField
    | DisplayField; 