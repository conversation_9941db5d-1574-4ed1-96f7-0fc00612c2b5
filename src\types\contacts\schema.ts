/**
 * Contact type definitions and schemas
 * @module types/contacts/schema
 */

import { z } from 'zod';

/**
 * Enum for contact relationships
 */
export enum ContactRelationship {
    FAMILY = 'Family',
    FRIEND = 'Friend',
    SPOUSE = 'Spouse',
    COLLEAGUE = 'Colleague',
    OTHER = 'Other'
}

/**
 * Enum for emergency contact relationships
 */
export enum EmergencyContactRelationship {
    PARENT = 'Parent',
    SPOUSE = 'Spouse',
    SIBLING = 'Sibling',
    FRIEND = 'Friend',
    OTHER = 'Other'
}

/**
 * Database schema for contacts matching Supabase table structure
 */
export const ContactDatabaseSchema = z.object({
    id: z.string().uuid().optional(),
    user_id: z.string(),
    first_name: z.string().min(1, 'First name is required'),
    last_name: z.string().nullable(),
    full_name: z.string().nullable(),
    relationship: z.string().min(1, 'Relationship is required'),
    email: z.string().email().nullable(),
    phone: z.string().nullable(),
    date_of_birth: z.string().nullable(), // date type in DB
    address: z.string().nullable(),
    city: z.string().nullable(),
    state: z.string().nullable(),
    country: z.string().nullable(),
    postcode: z.string().nullable(),
    tshirt_size: z.string().nullable(),
    gender: z.string().nullable(),
    emergency_contact: z.boolean().nullable(),
    emergency_contact_name: z.string().nullable(),
    emergency_contact_no: z.string().nullable(),
    emergency_contact_relationship: z.string().nullable(),
    notes: z.string().nullable(),
    created_at: z.string().datetime().optional(),
    updated_at: z.string().datetime().nullable()
});

/**
 * Type for database contact records
 */
export type ContactDatabase = z.infer<typeof ContactDatabaseSchema>;

/**
 * Frontend contact schema with transformed field names
 */
export const ContactSchema = z.object({
    id: z.string().uuid().optional(),
    userId: z.string(),
    firstName: z.string().min(1, 'First name is required'),
    lastName: z.string().nullable(),
    fullName: z.string().nullable(),
    relationship: z.nativeEnum(ContactRelationship),
    email: z.string().email().nullable(),
    phone: z.string().nullable(),
    dateOfBirth: z.string().nullable(),
    address: z.string().nullable(),
    city: z.string().nullable(),
    state: z.string().nullable(),
    country: z.string().nullable(),
    postcode: z.string().nullable(),
    tshirtSize: z.string().nullable(),
    gender: z.string().nullable(),
    isEmergencyContact: z.boolean().nullable(),
    emergencyContactName: z.string().nullable(),
    emergencyContactNo: z.string().nullable(),
    emergencyContactRelationship: z.nativeEnum(EmergencyContactRelationship).nullable(),
    notes: z.string().nullable(),
    createdAt: z.string().datetime().optional(),
    updatedAt: z.string().datetime().nullable()
});

/**
 * Type for frontend contact data
 */
export type Contact = z.infer<typeof ContactSchema>;

/**
 * Error type for contact operations
 */
export interface ContactError {
    code: string;
    message: string;
    details?: unknown;
}

/**
 * Response type for contact operations
 */
export interface ContactResponse<T> {
    data?: T;
    error?: ContactError;
}

/**
 * Transform database contact to frontend format
 */
export function transformDatabaseContact(contact: ContactDatabase): Contact {
    return {
        id: contact.id,
        userId: contact.user_id,
        firstName: contact.first_name,
        lastName: contact.last_name,
        fullName: contact.full_name,
        relationship: contact.relationship as ContactRelationship,
        email: contact.email,
        phone: contact.phone,
        dateOfBirth: contact.date_of_birth,
        address: contact.address,
        city: contact.city,
        state: contact.state,
        country: contact.country,
        postcode: contact.postcode,
        tshirtSize: contact.tshirt_size,
        gender: contact.gender,
        isEmergencyContact: contact.emergency_contact,
        emergencyContactName: contact.emergency_contact_name,
        emergencyContactNo: contact.emergency_contact_no,
        emergencyContactRelationship: contact.emergency_contact_relationship as EmergencyContactRelationship,
        notes: contact.notes,
        createdAt: contact.created_at,
        updatedAt: contact.updated_at
    };
}

/**
 * Transform frontend contact to database format
 */
export function transformContactToDatabase(contact: Contact): ContactDatabase {
    return {
        id: contact.id,
        user_id: contact.userId,
        first_name: contact.firstName,
        last_name: contact.lastName,
        full_name: contact.fullName,
        relationship: contact.relationship,
        email: contact.email,
        phone: contact.phone,
        date_of_birth: contact.dateOfBirth,
        address: contact.address,
        city: contact.city,
        state: contact.state,
        country: contact.country,
        postcode: contact.postcode,
        tshirt_size: contact.tshirtSize,
        gender: contact.gender,
        emergency_contact: contact.isEmergencyContact,
        emergency_contact_name: contact.emergencyContactName,
        emergency_contact_no: contact.emergencyContactNo,
        emergency_contact_relationship: contact.emergencyContactRelationship,
        notes: contact.notes,
        created_at: contact.createdAt,
        updated_at: contact.updatedAt
    };
} 