'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { createClient } from '@/lib/supabase/client'

interface User {
    id: string
    email: string
    first_name?: string
    last_name?: string | null
    avatar?: string | null
}

interface AuthContextType {
    user: User | null
    signIn: (credentials: { email: string; password: string }) => Promise<void>
    signUp: (credentials: { email: string; password: string }) => Promise<void>
    signOut: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
    const [user, setUser] = useState<User | null>(null)
    const supabase = createClient()

    useEffect(() => {
        const getUserData = async () => {
            try {
                const { data: { session } } = await supabase.auth.getSession()

                if (session) {
                    const { data: userData } = await supabase
                        .from('users')
                        .select('id, email, first_name, last_name, avatar')
                        .eq('auth_user_id', session.user.id)
                        .single()

                    setUser(userData)
                }
            } catch (error) {
                console.error('Error fetching user data:', error)
            }
        }

        getUserData()

        const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
            if (session) {
                const { data: userData } = await supabase
                    .from('users')
                    .select('id, email, first_name, last_name, avatar')
                    .eq('auth_user_id', session.user.id)
                    .single()

                setUser(userData)
            } else {
                setUser(null)
            }
        })

        return () => {
            subscription.unsubscribe()
        }
    }, [supabase])

    const signIn = async ({ email, password }: { email: string; password: string }) => {
        const { error } = await supabase.auth.signInWithPassword({ email, password })
        if (error) throw error
    }

    const signUp = async ({ email, password }: { email: string; password: string }) => {
        const { error } = await supabase.auth.signUp({ email, password })
        if (error) throw error
    }

    const signOut = async () => {
        const { error } = await supabase.auth.signOut()
        if (error) throw error
        setUser(null)
    }

    return (
        <AuthContext.Provider value={{ user, signIn, signUp, signOut }}>
            {children}
        </AuthContext.Provider>
    )
}

export function useAuth() {
    const context = useContext(AuthContext)
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider')
    }
    return context
} 