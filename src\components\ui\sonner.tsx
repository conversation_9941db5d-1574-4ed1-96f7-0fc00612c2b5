"use client"

import { useTheme } from "next-themes"
import { Toaster as Son<PERSON> } from "sonner"
import { ToastComponentProps } from "@/types/ui"

type ToasterProps = React.ComponentProps<typeof Sonner>

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = "system" } = useTheme()
  // Ensure theme is one of the allowed values and never undefined
  const validTheme: "light" | "dark" | "system" =
    theme === "light" ? "light" :
      theme === "dark" ? "dark" :
        "system"

  return (
    <Sonner
      theme={validTheme}
      className="toaster group"
      toastOptions={{
        classNames: {
          toast:
            "group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",
          description: "group-[.toast]:text-muted-foreground",
          actionButton:
            "group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",
          cancelButton:
            "group-[.toast]:bg-muted group-[.toast]:text-muted-foreground",
        },
      }}
      {...props} />
  )
}

export { Toaster }
